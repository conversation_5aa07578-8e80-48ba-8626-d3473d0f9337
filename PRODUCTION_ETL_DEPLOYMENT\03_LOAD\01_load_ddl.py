#!/usr/bin/env python3
"""
🌟 CORRECT STAR SCHEMA DDL - LOAD SCHEMA
Construction du schéma load avec VRAIE architecture star schema

ARCHITECTURE CORRECTE:
1. fact_jiraissue = COPIE EXACTE de transform.jiraissue_clean + 51 colonnes FK
2. 51 tables dimension = COPIE EXACTE des 51 autres tables transform
3. PK sur chaque table (colonne 'id' originale)
4. FK de fact_jiraissue vers chaque dimension

⚠️ SUPPRIME ET RECRÉE LE SCHÉMA LOAD COMPLET
"""

import psycopg2
import logging
from datetime import datetime

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'correct_load_ddl_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CorrectStarSchemaBuilder:
    """
    Construction CORRECTE du star schema:
    - fact_jiraissue = jiraissue data + 51 FK
    - 51 dimensions = autres tables transform
    """
    
    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # 51 tables dimension (TOUTES SAUF jiraissue)
        self.dimension_tables = [
            # CORE_BUSINESS (11 tables - sans jiraissue)
            'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',
            
            # USERS_GROUPS (8 tables)
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',
            
            # WORKFLOWS (5 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',
            
            # CONFIGURATION (6 tables)
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',
            
            # CHANGES_HISTORY (3 tables)
            'changegroup', 'changeitem', 'jiraaction',
            
            # PLUGINS_MANAGEMENT (2 tables)
            'pluginversion', 'managedconfigurationitem',
            
            # LOOKUP_TABLES (6 tables)
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',
            
            # SCRIPT_RUNNER (4 tables)
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',
            
            # AGILE_BOARDS (3 tables)
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',
            
            # JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]
        
        self.stats = {
            'start_time': datetime.now(),
            'tables_created': 0,
            'errors': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f"Erreur connexion DW: {e}")
            raise
    
    def create_load_schema(self):
        """Créer le schéma load (supprime l'ancien)"""
        logger.info("🏗️ CRÉATION SCHÉMA LOAD CORRECT")
        logger.info("=" * 60)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Supprimer le schéma existant
            cursor.execute('DROP SCHEMA IF EXISTS load CASCADE')
            logger.info("🗑️ Ancien schéma load supprimé")
            
            # Créer le nouveau schéma
            cursor.execute('CREATE SCHEMA load')
            logger.info("✅ Nouveau schéma load créé")
            
            dw_conn.commit()
            
        except Exception as e:
            logger.error(f"❌ Erreur création schéma: {e}")
            dw_conn.rollback()
            raise
        finally:
            dw_conn.close()
    
    def get_transform_table_structure(self, table_name):
        """Récupérer la structure EXACTE d'une table transform"""
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            transform_table_name = f"{table_name}_clean"
            
            cursor.execute("""
                SELECT 
                    column_name,
                    data_type,
                    character_maximum_length,
                    numeric_precision,
                    numeric_scale,
                    is_nullable,
                    ordinal_position
                FROM information_schema.columns
                WHERE table_schema = 'transform' AND table_name = %s
                ORDER BY ordinal_position
            """, (transform_table_name,))
            
            columns = cursor.fetchall()
            
            if not columns:
                logger.warning(f"⚠️ Table transform.{transform_table_name} non trouvée")
                return None
            
            return columns
            
        except Exception as e:
            logger.error(f"❌ Erreur récupération structure {table_name}: {e}")
            return None
        finally:
            dw_conn.close()
    
    def build_column_definition(self, col_name, data_type, char_max_len, num_precision, num_scale, is_nullable):
        """Construire une définition de colonne PostgreSQL EXACTE"""
        
        # Construire le type PostgreSQL EXACT
        if data_type == 'character varying':
            if char_max_len:
                pg_type = f'VARCHAR({char_max_len})'
            else:
                pg_type = 'TEXT'
        elif data_type == 'character':
            if char_max_len:
                pg_type = f'CHAR({char_max_len})'
            else:
                pg_type = 'CHAR(1)'
        elif data_type == 'text':
            pg_type = 'TEXT'
        elif data_type == 'numeric':
            if num_precision and num_scale is not None:
                pg_type = f'NUMERIC({num_precision},{num_scale})'
            elif num_precision:
                pg_type = f'NUMERIC({num_precision})'
            else:
                pg_type = 'NUMERIC'
        elif data_type == 'integer':
            pg_type = 'INTEGER'
        elif data_type == 'bigint':
            pg_type = 'BIGINT'
        elif data_type == 'smallint':
            pg_type = 'SMALLINT'
        elif data_type == 'double precision':
            pg_type = 'DOUBLE PRECISION'
        elif data_type == 'real':
            pg_type = 'REAL'
        elif data_type == 'timestamp with time zone':
            pg_type = 'TIMESTAMPTZ'
        elif data_type == 'timestamp without time zone':
            pg_type = 'TIMESTAMP'
        elif data_type == 'date':
            pg_type = 'DATE'
        elif data_type == 'boolean':
            pg_type = 'BOOLEAN'
        elif data_type == 'bytea':
            pg_type = 'BYTEA'
        else:
            pg_type = 'TEXT'
            logger.warning(f"⚠️ Type non reconnu '{data_type}' pour {col_name}")
        
        # Contrainte NULL/NOT NULL
        null_constraint = '' if is_nullable == 'YES' else ' NOT NULL'
        
        return f'    "{col_name}" {pg_type}{null_constraint}'

    def create_dimension_table(self, table_name):
        """Créer une table dimension (copie exacte de transform)"""
        logger.info(f"🔷 Création dim_{table_name}")

        # Récupérer la structure transform
        transform_columns = self.get_transform_table_structure(table_name)
        if not transform_columns:
            logger.error(f"❌ Structure transform manquante pour {table_name}")
            return False

        # Construire les définitions de colonnes
        column_definitions = []
        for col_name, data_type, char_max_len, num_precision, num_scale, is_nullable, position in transform_columns:
            col_def = self.build_column_definition(col_name, data_type, char_max_len, num_precision, num_scale, is_nullable)
            column_definitions.append(col_def)

        # Créer la table dimension
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()

        try:
            dim_table_name = f"dim_{table_name}"
            columns_ddl = ',\n'.join(column_definitions)

            create_sql = f'''
                CREATE TABLE load."{dim_table_name}" (
                {columns_ddl}
                )
            '''

            cursor.execute(f'DROP TABLE IF EXISTS load."{dim_table_name}" CASCADE')
            cursor.execute(create_sql)

            # Ajouter PK si colonne 'id' existe
            has_id = any(col[0] == 'id' for col in transform_columns)
            if has_id:
                pk_sql = f'ALTER TABLE load."{dim_table_name}" ADD CONSTRAINT "pk_{dim_table_name}" PRIMARY KEY ("id")'
                cursor.execute(pk_sql)
                logger.info(f"   ✅ PK ajoutée sur {dim_table_name}.id")

            dw_conn.commit()
            self.stats['tables_created'] += 1
            logger.info(f"   ✅ {dim_table_name}: {len(column_definitions)} colonnes")
            return True

        except Exception as e:
            logger.error(f"   ❌ Erreur création {dim_table_name}: {e}")
            dw_conn.rollback()
            self.stats['errors'].append(f"{table_name}: {e}")
            return False
        finally:
            dw_conn.close()

    def create_all_dimension_tables(self):
        """Créer toutes les tables dimension (51 tables)"""
        logger.info(f"\n🔷 CRÉATION {len(self.dimension_tables)} TABLES DIMENSION")
        logger.info("=" * 70)

        success_count = 0
        for i, table_name in enumerate(self.dimension_tables, 1):
            logger.info(f"[{i:2d}/{len(self.dimension_tables)}] {table_name}")
            if self.create_dimension_table(table_name):
                success_count += 1

        logger.info(f"\n📊 Dimensions créées: {success_count}/{len(self.dimension_tables)}")
        return success_count

    def create_fact_jiraissue_table(self):
        """Créer fact_jiraissue = jiraissue data + 51 FK colonnes"""
        logger.info(f"\n📊 CRÉATION FACT_JIRAISSUE")
        logger.info("=" * 70)

        # Récupérer la structure jiraissue de transform
        jiraissue_columns = self.get_transform_table_structure('jiraissue')
        if not jiraissue_columns:
            logger.error("❌ Structure jiraissue manquante")
            return False

        # Construire les colonnes jiraissue (EXACT COPY)
        jiraissue_defs = []
        for col_name, data_type, char_max_len, num_precision, num_scale, is_nullable, position in jiraissue_columns:
            col_def = self.build_column_definition(col_name, data_type, char_max_len, num_precision, num_scale, is_nullable)
            jiraissue_defs.append(col_def)

        # Construire les colonnes FK (51 colonnes)
        fk_defs = []
        for table_name in self.dimension_tables:
            fk_column = f"{table_name}_id"
            # Type FK basé sur le type de la dimension
            if table_name in ['project', 'component', 'customfield', 'worklog', 'fileattachment']:
                fk_type = "BIGINT"
            else:
                fk_type = "INTEGER"
            fk_defs.append(f'    "{fk_column}" {fk_type}')

        # Créer fact_jiraissue
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()

        try:
            all_columns = jiraissue_defs + fk_defs
            columns_ddl = ',\n'.join(all_columns)

            create_sql = f'''
                CREATE TABLE load.fact_jiraissue (
                {columns_ddl}
                )
            '''

            cursor.execute('DROP TABLE IF EXISTS load.fact_jiraissue CASCADE')
            cursor.execute(create_sql)

            # Ajouter PK sur 'id' (colonne originale de jiraissue)
            pk_sql = 'ALTER TABLE load.fact_jiraissue ADD CONSTRAINT "pk_fact_jiraissue" PRIMARY KEY ("id")'
            cursor.execute(pk_sql)

            dw_conn.commit()
            self.stats['tables_created'] += 1

            logger.info(f"   ✅ fact_jiraissue créée")
            logger.info(f"   📊 Colonnes jiraissue: {len(jiraissue_defs)}")
            logger.info(f"   🔗 Colonnes FK: {len(fk_defs)}")
            logger.info(f"   📈 Total colonnes: {len(all_columns)}")
            logger.info(f"   🔑 PK sur fact_jiraissue.id")

            return True

        except Exception as e:
            logger.error(f"❌ Erreur création fact_jiraissue: {e}")
            dw_conn.rollback()
            self.stats['errors'].append(f"fact_jiraissue: {e}")
            return False
        finally:
            dw_conn.close()

    def create_foreign_keys(self):
        """Créer les FK de fact_jiraissue vers les dimensions"""
        logger.info(f"\n🔗 CRÉATION FOREIGN KEYS")
        logger.info("=" * 70)

        fk_count = 0
        for i, table_name in enumerate(self.dimension_tables, 1):
            logger.info(f"[{i:2d}/{len(self.dimension_tables)}] FK pour {table_name}")

            # Chaque FK dans sa propre transaction
            dw_conn = self.connect_dw()
            cursor = dw_conn.cursor()

            try:
                fk_column = f"{table_name}_id"
                dim_table = f"dim_{table_name}"
                constraint_name = f"fk_fact_jiraissue_{table_name}"

                # Vérifier que la dimension existe et a une PK
                cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.table_constraints
                    WHERE table_schema = 'load' AND table_name = %s
                    AND constraint_type = 'PRIMARY KEY'
                """, (dim_table,))

                if cursor.fetchone()[0] == 0:
                    logger.warning(f"   ⚠️ PK manquante dans {dim_table} - SKIP")
                    continue

                # Créer la FK
                fk_sql = f'''
                    ALTER TABLE load.fact_jiraissue
                    ADD CONSTRAINT "{constraint_name}"
                    FOREIGN KEY ("{fk_column}")
                    REFERENCES load."{dim_table}"("id")
                    ON DELETE SET NULL
                    ON UPDATE CASCADE
                '''

                cursor.execute(fk_sql)
                dw_conn.commit()
                fk_count += 1

                logger.info(f"   ✅ FK {fk_column} -> {dim_table}.id")

            except Exception as e:
                logger.warning(f"   ❌ FK {table_name}: {str(e)[:80]}")
                dw_conn.rollback()
            finally:
                dw_conn.close()

        logger.info(f"\n🔗 FK créées: {fk_count}/{len(self.dimension_tables)}")
        return fk_count

def main():
    """Point d'entrée principal"""
    print("🌟 CONSTRUCTION STAR SCHEMA CORRECT - LOAD")
    print("=" * 60)
    print("🎯 fact_jiraissue = jiraissue data + 51 FK")
    print("🔷 51 dimensions = autres tables transform")
    print("🔗 PK + FK relationships corrects")
    print("📊 100% data integrity required")
    print("=" * 60)

    builder = CorrectStarSchemaBuilder()

    try:
        # 1. Créer le schéma load
        builder.create_load_schema()

        # 2. Créer toutes les tables dimension (51 tables)
        dim_count = builder.create_all_dimension_tables()

        # 3. Créer fact_jiraissue avec FK colonnes
        fact_success = builder.create_fact_jiraissue_table()

        # 4. Créer les foreign keys
        fk_count = builder.create_foreign_keys()

        # Statistiques finales
        duration = (datetime.now() - builder.stats['start_time']).total_seconds()

        logger.info(f"\n🎉 CONSTRUCTION STAR SCHEMA TERMINÉE!")
        logger.info(f"⏱️ Durée: {duration:.2f} secondes")
        logger.info(f"🔷 Dimensions créées: {dim_count}/51")
        logger.info(f"📊 Fact table: {'✅' if fact_success else '❌'}")
        logger.info(f"🔗 Foreign keys: {fk_count}/51")
        logger.info(f"❌ Erreurs: {len(builder.stats['errors'])}")

        if builder.stats['errors']:
            logger.warning("🚨 Erreurs rencontrées:")
            for error in builder.stats['errors'][:5]:
                logger.warning(f"   - {error}")

        if dim_count >= 45 and fact_success and fk_count >= 40:
            logger.info(f"\n🎯 STAR SCHEMA RÉUSSI!")
            logger.info(f"   Architecture correcte créée")
            logger.info(f"   Prêt pour l'insertion des données")
            logger.info(f"\n📋 PROCHAINE ÉTAPE:")
            logger.info(f"   Exécuter: python 02_load_insert.py")
        else:
            logger.warning(f"\n⚠️ STAR SCHEMA PARTIEL")
            logger.warning(f"   Vérifier les erreurs ci-dessus")

    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
