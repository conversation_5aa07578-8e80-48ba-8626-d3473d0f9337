#!/usr/bin/env python3
"""
🔄 CONSTRUCTION SCHÉMA LOAD - NEW APPROACH
Construction du schéma load avec types EXACTS copiés depuis transform
+ Ajout de tables fact et dimension pour analytics (NO DENORMALIZATION)

APPROACH:
1. Copy ALL 52 tables from transform schema (EXACT COPY)
2. Add fact and dimension tables on top
3. Create FK relationships in fact tables
4. Avoid denormalization issues

⚠️ ATTENTION: Supprime et recrée le schéma load complet
"""

import psycopg2
import logging
from datetime import datetime
import time

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'build_load_new_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LoadSchemaBuilder:
    """
    Construction du schéma load avec types EXACTS depuis transform
    + Tables fact et dimension pour analytics (NO DENORMALIZATION)
    """
    
    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # 52 tables critiques complètes (SAME AS TRANSFORM)
        self.all_critical_tables = [
            # 🔥 CORE_BUSINESS (12 tables)
            'jiraissue', 'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',
            
            # 👥 USERS_GROUPS (8 tables)
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',
            
            # 🔄 WORKFLOWS (5 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',
            
            # ⚙️ CONFIGURATION (6 tables)
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',
            
            # 📝 CHANGES_HISTORY (3 tables)
            'changegroup', 'changeitem', 'jiraaction',
            
            # 🔧 PLUGINS_MANAGEMENT (2 tables)
            'pluginversion', 'managedconfigurationitem',
            
            # 📊 LOOKUP_TABLES (6 tables)
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',
            
            # 🤖 SCRIPT_RUNNER (4 tables)
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',
            
            # 🎯 AGILE_BOARDS (3 tables)
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',
            
            # 🔍 JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]
        
        # STAR SCHEMA: 1 FACT + 52 DIMENSION TABLES
        # Each of the 52 tables becomes a dimension table
        # fact_jiraissue is the central fact table with FKs to all dimensions
        
        self.stats = {
            'start_time': datetime.now(),
            'tables_created': 0,
            'fact_tables': 0,
            'dimension_tables': 0,
            'total_columns': 0,
            'errors': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f"❌ Erreur connexion DW: {e}")
            raise
    
    def create_load_schema(self):
        """Créer le schéma load"""
        logger.info("🏗️ CRÉATION SCHÉMA LOAD")
        logger.info("=" * 60)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Supprimer le schéma existant
            cursor.execute('DROP SCHEMA IF EXISTS load CASCADE')
            logger.info("🗑️ Ancien schéma load supprimé")
            
            # Créer le nouveau schéma
            cursor.execute('CREATE SCHEMA load')
            logger.info("✅ Nouveau schéma load créé")
            
            dw_conn.commit()
            
        except Exception as e:
            logger.error(f"❌ Erreur création schéma: {e}")
            dw_conn.rollback()
            raise
        finally:
            dw_conn.close()
    
    def get_transform_table_structure(self, table_name):
        """Récupérer la structure EXACTE d'une table transform"""
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            transform_table_name = f"{table_name}_clean"
            
            cursor.execute("""
                SELECT 
                    column_name,
                    data_type,
                    character_maximum_length,
                    numeric_precision,
                    numeric_scale,
                    is_nullable,
                    ordinal_position
                FROM information_schema.columns
                WHERE table_schema = 'transform' AND table_name = %s
                ORDER BY ordinal_position
            """, (transform_table_name,))
            
            columns = cursor.fetchall()
            
            if not columns:
                logger.warning(f"⚠️ Table transform.{transform_table_name} non trouvée")
                return None
            
            return columns
            
        except Exception as e:
            logger.error(f"❌ Erreur récupération structure {table_name}: {e}")
            return None
        finally:
            dw_conn.close()
    
    def build_load_column_definitions(self, table_name, transform_columns):
        """Construire les définitions de colonnes pour load (EXACT COPY from transform)"""
        column_definitions = []
        
        for col_name, data_type, char_max_len, num_precision, num_scale, is_nullable, position in transform_columns:
            
            # Construire le type PostgreSQL EXACT (copié de transform)
            if data_type == 'character varying':
                if char_max_len:
                    pg_type = f'VARCHAR({char_max_len})'
                else:
                    pg_type = 'TEXT'
            elif data_type == 'character':
                if char_max_len:
                    pg_type = f'CHAR({char_max_len})'
                else:
                    pg_type = 'CHAR(1)'
            elif data_type == 'text':
                pg_type = 'TEXT'
            elif data_type == 'numeric':
                if num_precision and num_scale is not None:
                    pg_type = f'NUMERIC({num_precision},{num_scale})'
                elif num_precision:
                    pg_type = f'NUMERIC({num_precision})'
                else:
                    pg_type = 'NUMERIC'
            elif data_type == 'integer':
                pg_type = 'INTEGER'
            elif data_type == 'bigint':
                pg_type = 'BIGINT'
            elif data_type == 'smallint':
                pg_type = 'SMALLINT'
            elif data_type == 'double precision':
                pg_type = 'DOUBLE PRECISION'
            elif data_type == 'real':
                pg_type = 'REAL'
            elif data_type == 'timestamp with time zone':
                pg_type = 'TIMESTAMPTZ'
            elif data_type == 'timestamp without time zone':
                pg_type = 'TIMESTAMP'
            elif data_type == 'date':
                pg_type = 'DATE'
            elif data_type == 'boolean':
                pg_type = 'BOOLEAN'
            elif data_type == 'bytea':
                pg_type = 'BYTEA'
            else:
                pg_type = 'TEXT'
                logger.warning(f"⚠️ Type non reconnu '{data_type}' pour {table_name}.{col_name}")
            
            # Contrainte NULL/NOT NULL
            null_constraint = '' if is_nullable == 'YES' else ' NOT NULL'
            
            # EXACT COPY: Colonne identique à transform
            column_def = f'    "{col_name}" {pg_type}{null_constraint}'
            column_definitions.append(column_def)
        
        return column_definitions

    def create_dimension_table(self, table_name, column_definitions):
        """Créer une table dimension avec types EXACTS (copie de transform)"""
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()

        try:
            # Nom de table dimension: dim_[table_name]
            dim_table_name = f"dim_{table_name}"

            # Créer la table
            columns_ddl = ',\n'.join(column_definitions)
            create_sql = f'''
                CREATE TABLE load."{dim_table_name}" (
                {columns_ddl}
                )
            '''

            cursor.execute(f'DROP TABLE IF EXISTS load."{dim_table_name}" CASCADE')
            cursor.execute(create_sql)
            dw_conn.commit()

            self.stats['tables_created'] += 1
            self.stats['total_columns'] += len(column_definitions)

            logger.info(f"   ✅ load.{dim_table_name}: {len(column_definitions)} colonnes")

        except Exception as e:
            logger.error(f"   ❌ Erreur création load.dim_{table_name}: {e}")
            dw_conn.rollback()
            self.stats['errors'].append(f"{table_name}: {e}")
            raise
        finally:
            dw_conn.close()

    def build_all_dimension_tables(self):
        """Construire toutes les tables dimension (52 tables)"""
        logger.info(f"🔷 CONSTRUCTION {len(self.all_critical_tables)} TABLES DIMENSION")
        logger.info("=" * 70)

        for i, table_name in enumerate(self.all_critical_tables, 1):
            logger.info(f"🔧 [{i:2d}/{len(self.all_critical_tables)}] Construction dim_{table_name}")

            try:
                # Récupérer la structure transform
                transform_columns = self.get_transform_table_structure(table_name)

                if transform_columns:
                    # Construire les définitions dimension (EXACT COPY)
                    column_definitions = self.build_load_column_definitions(table_name, transform_columns)

                    # Créer la table dimension
                    self.create_dimension_table(table_name, column_definitions)

                    logger.info(f"   📊 {table_name}: {len(transform_columns)} transform → {len(column_definitions)} dimension")
                    self.stats['dimension_tables'] += 1
                else:
                    logger.error(f"   ❌ Impossible de récupérer la structure transform pour {table_name}")
                    self.stats['errors'].append(f"{table_name}: Structure transform non trouvée")

            except Exception as e:
                logger.warning(f"   ⚠️ {table_name}: Erreur construction - {str(e)[:50]}")
                self.stats['errors'].append(f"{table_name}: {e}")
                continue

    def create_fact_jiraissue_table(self):
        """Créer la table fact_jiraissue avec FK vers toutes les dimensions"""
        logger.info(f"\n📊 CRÉATION TABLE FACT_JIRAISSUE")
        logger.info("=" * 70)

        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()

        try:
            # Construire les colonnes FK pour chaque table dimension
            fk_columns = []

            for table_name in self.all_critical_tables:
                # Chaque table devient une FK dans fact_jiraissue
                fk_column = f"{table_name}_id"

                # Déterminer le type de données pour la FK
                if table_name in ['jiraissue', 'project', 'component', 'customfield', 'worklog', 'fileattachment']:
                    fk_type = "BIGINT"
                else:
                    fk_type = "INTEGER"

                fk_columns.append(f"    {fk_column} {fk_type}")

            # Colonnes de base de la fact table
            base_columns = [
                "    fact_id BIGSERIAL PRIMARY KEY",
                "    instance_id INTEGER NOT NULL",
                "    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
            ]

            # Combiner toutes les colonnes
            all_columns = base_columns + fk_columns
            columns_ddl = ',\n'.join(all_columns)

            # Créer fact_jiraissue
            logger.info("📊 Création fact_jiraissue avec FK vers toutes les dimensions")
            create_sql = f'''
                CREATE TABLE load.fact_jiraissue (
                {columns_ddl}
                )
            '''

            cursor.execute('DROP TABLE IF EXISTS load.fact_jiraissue CASCADE')
            cursor.execute(create_sql)

            self.stats['fact_tables'] += 1
            logger.info(f"   ✅ fact_jiraissue créée avec {len(fk_columns)} colonnes FK")

            dw_conn.commit()
            logger.info(f"📊 fact_jiraissue créée avec {len(self.all_critical_tables)} colonnes FK")

        except Exception as e:
            logger.error(f"❌ Erreur création fact/dimension tables: {e}")
            dw_conn.rollback()
            raise
        finally:
            dw_conn.close()

    def create_foreign_keys(self):
        """Créer les clés étrangères de fact_jiraissue vers toutes les dimensions"""
        logger.info(f"\n🔗 CRÉATION CLÉS ÉTRANGÈRES")
        logger.info("=" * 70)

        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()

        try:
            fk_count = 0

            for table_name in self.all_critical_tables:
                try:
                    # Nom de la colonne FK dans fact_jiraissue
                    fk_column = f"{table_name}_id"

                    # Nom de la table dimension
                    dim_table = f"dim_{table_name}"

                    # Vérifier que la dimension table existe
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.tables
                        WHERE table_schema = 'load' AND table_name = %s
                    """, (dim_table,))

                    if cursor.fetchone()[0] == 0:
                        logger.warning(f"   ⚠️ Table {dim_table} n'existe pas")
                        continue

                    # Vérifier que la colonne 'id' existe dans la dimension
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.columns
                        WHERE table_schema = 'load' AND table_name = %s AND column_name = 'id'
                    """, (dim_table,))

                    if cursor.fetchone()[0] == 0:
                        logger.warning(f"   ⚠️ Colonne 'id' manquante dans {dim_table}")
                        continue

                    # Nom de la contrainte FK
                    constraint_name = f"fk_fact_jiraissue_{table_name}"

                    # Créer la contrainte FK
                    fk_sql = f'''
                        ALTER TABLE load.fact_jiraissue
                        ADD CONSTRAINT "{constraint_name}"
                        FOREIGN KEY ("{fk_column}")
                        REFERENCES load."{dim_table}"("id")
                        ON DELETE SET NULL
                        ON UPDATE CASCADE
                    '''

                    cursor.execute(fk_sql)
                    fk_count += 1

                    logger.info(f"   ✅ FK fact_jiraissue.{fk_column} → {dim_table}.id")

                except Exception as e:
                    logger.warning(f"   ⚠️ FK {table_name}: {str(e)[:100]}")
                    continue

            dw_conn.commit()
            logger.info(f"🔗 {fk_count}/{len(self.all_critical_tables)} clés étrangères créées")

            return fk_count

        except Exception as e:
            logger.error(f"❌ Erreur création FK: {e}")
            dw_conn.rollback()
            raise
        finally:
            dw_conn.close()

    def verify_load_schema(self):
        """Vérifier le schéma load créé"""
        logger.info("\n🔍 VÉRIFICATION SCHÉMA LOAD")
        logger.info("=" * 60)

        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()

        try:
            # Lister toutes les tables load
            cursor.execute("""
                SELECT table_name,
                       (SELECT COUNT(*) FROM information_schema.columns
                        WHERE table_schema = 'load' AND table_name = t.table_name) as column_count
                FROM information_schema.tables t
                WHERE table_schema = 'load'
                ORDER BY table_name
            """)

            load_tables = cursor.fetchall()

            logger.info(f"📊 Tables load créées: {len(load_tables)}")

            # Séparer les tables par type
            regular_tables = []
            fact_dim_tables = []

            for table_name, column_count in load_tables:
                if table_name.startswith('fact_') or table_name.startswith('dim_'):
                    fact_dim_tables.append((table_name, column_count))
                else:
                    regular_tables.append((table_name, column_count))

            logger.info(f"   📋 Tables régulières: {len(regular_tables)}")
            for table_name, column_count in regular_tables[:5]:  # Afficher les 5 premières
                logger.info(f"      ✅ load.{table_name}: {column_count} colonnes")
            if len(regular_tables) > 5:
                logger.info(f"      ... et {len(regular_tables) - 5} autres tables")

            logger.info(f"   🌟 Tables fact/dimension: {len(fact_dim_tables)}")
            for table_name, column_count in fact_dim_tables:
                logger.info(f"      ✅ load.{table_name}: {column_count} colonnes")

            return len(load_tables)

        finally:
            dw_conn.close()

def main():
    """Point d'entrée principal"""
    print("🌟 CONSTRUCTION SCHÉMA LOAD - STAR SCHEMA")
    print("=" * 60)
    print("🔷 52 tables dimension (copie exacte depuis transform)")
    print("📊 1 table fact_jiraissue (centre de l'étoile)")
    print("🔗 52 clés étrangères (fact → dimensions)")
    print("⭐ STAR SCHEMA PARFAIT")
    print("=" * 60)

    builder = LoadSchemaBuilder()

    try:
        # Créer le schéma load
        builder.create_load_schema()

        # Construire toutes les tables dimension (52 tables)
        builder.build_all_dimension_tables()

        # Créer la table fact_jiraissue
        builder.create_fact_jiraissue_table()

        # Créer les clés étrangères (52 FK)
        fk_count = builder.create_foreign_keys()

        # Vérifier le schéma créé
        total_tables = builder.verify_load_schema()

        # Statistiques finales
        duration = (datetime.now() - builder.stats['start_time']).total_seconds()

        logger.info(f"\n🎉 CONSTRUCTION LOAD TERMINÉE!")
        logger.info(f"⏱️ Durée: {duration:.2f} secondes")
        logger.info(f"📊 Tables créées: {builder.stats['tables_created']}")
        logger.info(f"🌟 Fact tables: {builder.stats['fact_tables']}")
        logger.info(f"🔷 Dimension tables: {builder.stats['dimension_tables']}")
        logger.info(f"📈 Total colonnes: {builder.stats['total_columns']}")
        logger.info(f"❌ Erreurs: {len(builder.stats['errors'])}")

        if builder.stats['errors']:
            logger.warning(f"🚨 Erreurs rencontrées:")
            for error in builder.stats['errors'][:5]:
                logger.warning(f"   - {error}")

        expected_total = len(builder.all_critical_tables) + 1  # 52 dimensions + 1 fact
        if total_tables >= expected_total:
            logger.info(f"\n🎯 SCHÉMA LOAD RÉUSSI!")
            logger.info(f"   {total_tables} tables load disponibles")
            logger.info(f"   {len(builder.all_critical_tables)} tables dimension + 1 fact table")
            logger.info(f"   {fk_count} clés étrangères créées")
            logger.info(f"\n🎯 PROCHAINE ÉTAPE:")
            logger.info(f"   Exécuter: python 02_load_insert.py")
            logger.info(f"   Pour charger les données transform → load")
        else:
            logger.warning(f"\n⚠️ SCHÉMA LOAD PARTIEL")
            logger.warning(f"   Seulement {total_tables}/{expected_total} tables disponibles")

    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
