#!/usr/bin/env python3
"""
🌟 CORRECT STAR SCHEMA DDL - LOAD SCHEMA
Construction du schéma load avec VRAIE architecture star schema

ARCHITECTURE CORRECTE:
1. fact_jiraissue = COPIE EXACTE de transform.jiraissue_clean + 51 colonnes FK
2. 51 tables dimension = COPIE EXACTE des 51 autres tables transform
3. PK sur chaque table (colonne 'id' originale)
4. FK de fact_jiraissue vers chaque dimension

⚠️ SUPPRIME ET RECRÉE LE SCHÉMA LOAD COMPLET
"""

import psycopg2
import logging
from datetime import datetime

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'correct_load_ddl_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CorrectStarSchemaBuilder:
    """
    Construction CORRECTE du star schema:
    - fact_jiraissue = jiraissue data + 51 FK
    - 51 dimensions = autres tables transform
    """
    
    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # 51 tables dimension (TOUTES SAUF jiraissue)
        self.dimension_tables = [
            # CORE_BUSINESS (11 tables - sans jiraissue)
            'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',
            
            # USERS_GROUPS (8 tables)
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',
            
            # WORKFLOWS (5 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',
            
            # CONFIGURATION (6 tables)
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',
            
            # CHANGES_HISTORY (3 tables)
            'changegroup', 'changeitem', 'jiraaction',
            
            # PLUGINS_MANAGEMENT (2 tables)
            'pluginversion', 'managedconfigurationitem',
            
            # LOOKUP_TABLES (6 tables)
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',
            
            # SCRIPT_RUNNER (4 tables)
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',
            
            # AGILE_BOARDS (3 tables)
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',
            
            # JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]
        
        self.stats = {
            'start_time': datetime.now(),
            'tables_created': 0,
            'errors': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f"Erreur connexion DW: {e}")
            raise
    
    def create_load_schema(self):
        """Créer le schéma load (supprime l'ancien)"""
        logger.info("🏗️ CRÉATION SCHÉMA LOAD CORRECT")
        logger.info("=" * 60)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Supprimer le schéma existant
            cursor.execute('DROP SCHEMA IF EXISTS load CASCADE')
            logger.info("🗑️ Ancien schéma load supprimé")
            
            # Créer le nouveau schéma
            cursor.execute('CREATE SCHEMA load')
            logger.info("✅ Nouveau schéma load créé")
            
            dw_conn.commit()
            
        except Exception as e:
            logger.error(f"❌ Erreur création schéma: {e}")
            dw_conn.rollback()
            raise
        finally:
            dw_conn.close()
    
    def get_transform_table_structure(self, table_name):
        """Récupérer la structure EXACTE d'une table transform"""
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            transform_table_name = f"{table_name}_clean"
            
            cursor.execute("""
                SELECT 
                    column_name,
                    data_type,
                    character_maximum_length,
                    numeric_precision,
                    numeric_scale,
                    is_nullable,
                    ordinal_position
                FROM information_schema.columns
                WHERE table_schema = 'transform' AND table_name = %s
                ORDER BY ordinal_position
            """, (transform_table_name,))
            
            columns = cursor.fetchall()
            
            if not columns:
                logger.warning(f"⚠️ Table transform.{transform_table_name} non trouvée")
                return None
            
            return columns
            
        except Exception as e:
            logger.error(f"❌ Erreur récupération structure {table_name}: {e}")
            return None
        finally:
            dw_conn.close()
    
    def build_column_definition(self, col_name, data_type, char_max_len, num_precision, num_scale, is_nullable):
        """Construire une définition de colonne PostgreSQL EXACTE"""
        
        # Construire le type PostgreSQL EXACT
        if data_type == 'character varying':
            if char_max_len:
                pg_type = f'VARCHAR({char_max_len})'
            else:
                pg_type = 'TEXT'
        elif data_type == 'character':
            if char_max_len:
                pg_type = f'CHAR({char_max_len})'
            else:
                pg_type = 'CHAR(1)'
        elif data_type == 'text':
            pg_type = 'TEXT'
        elif data_type == 'numeric':
            if num_precision and num_scale is not None:
                pg_type = f'NUMERIC({num_precision},{num_scale})'
            elif num_precision:
                pg_type = f'NUMERIC({num_precision})'
            else:
                pg_type = 'NUMERIC'
        elif data_type == 'integer':
            pg_type = 'INTEGER'
        elif data_type == 'bigint':
            pg_type = 'BIGINT'
        elif data_type == 'smallint':
            pg_type = 'SMALLINT'
        elif data_type == 'double precision':
            pg_type = 'DOUBLE PRECISION'
        elif data_type == 'real':
            pg_type = 'REAL'
        elif data_type == 'timestamp with time zone':
            pg_type = 'TIMESTAMPTZ'
        elif data_type == 'timestamp without time zone':
            pg_type = 'TIMESTAMP'
        elif data_type == 'date':
            pg_type = 'DATE'
        elif data_type == 'boolean':
            pg_type = 'BOOLEAN'
        elif data_type == 'bytea':
            pg_type = 'BYTEA'
        else:
            pg_type = 'TEXT'
            logger.warning(f"⚠️ Type non reconnu '{data_type}' pour {col_name}")
        
        # Contrainte NULL/NOT NULL
        null_constraint = '' if is_nullable == 'YES' else ' NOT NULL'
        
        return f'    "{col_name}" {pg_type}{null_constraint}'

def main():
    """Point d'entrée principal"""
    print("🌟 CONSTRUCTION STAR SCHEMA CORRECT - LOAD")
    print("=" * 60)
    print("🎯 fact_jiraissue = jiraissue data + 51 FK")
    print("🔷 51 dimensions = autres tables transform")
    print("🔗 PK + FK relationships corrects")
    print("📊 100% data integrity required")
    print("=" * 60)
    
    builder = CorrectStarSchemaBuilder()
    
    try:
        # Créer le schéma load
        builder.create_load_schema()
        
        logger.info(f"\n✅ SCHÉMA LOAD CORRECT CRÉÉ!")
        logger.info(f"📋 Prochaines étapes:")
        logger.info(f"   1. Créer 51 tables dimension")
        logger.info(f"   2. Créer fact_jiraissue avec FK")
        logger.info(f"   3. Ajouter PK et FK constraints")
        logger.info(f"   4. Insérer les données")
        
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
