#!/usr/bin/env python3
"""
🔗 CRÉATION DES CLÉS ÉTRANGÈRES - STAR SCHEMA
Création des 52 clés étrangères entre fact_jiraissue et toutes les dimensions

OBJECTIF:
- Connecter fact_jiraissue à toutes les tables dimension
- Créer 52 relations FK parfaites
- Valider l'intégrité référentielle
- Compléter l'architecture star schema

⚠️ PRÉREQUIS: Schéma load avec fact_jiraissue et 52 tables dim_* créées
"""

import psycopg2
import logging
from datetime import datetime

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'create_fks_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ForeignKeyCreator:
    """
    Création des clés étrangères pour le star schema
    fact_jiraissue → 52 tables dimension
    """
    
    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # 52 tables critiques (SAME AS DDL SCRIPT)
        self.all_critical_tables = [
            # CORE_BUSINESS (12 tables)
            'jiraissue', 'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',
            
            # USERS_GROUPS (8 tables)
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',
            
            # WORKFLOWS (5 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',
            
            # CONFIGURATION (6 tables)
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',
            
            # CHANGES_HISTORY (3 tables)
            'changegroup', 'changeitem', 'jiraaction',
            
            # PLUGINS_MANAGEMENT (2 tables)
            'pluginversion', 'managedconfigurationitem',
            
            # LOOKUP_TABLES (6 tables)
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',
            
            # SCRIPT_RUNNER (4 tables)
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',
            
            # AGILE_BOARDS (3 tables)
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',
            
            # JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]
        
        self.stats = {
            'start_time': datetime.now(),
            'fks_created': 0,
            'fks_failed': 0,
            'errors': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f"Erreur connexion DW: {e}")
            raise
    
    def verify_prerequisites(self):
        """Vérifier que les prérequis sont en place"""
        logger.info("VERIFICATION DES PREREQUIS")
        logger.info("=" * 60)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Vérifier fact_jiraissue
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
            """)
            
            if cursor.fetchone()[0] == 0:
                raise Exception("Table fact_jiraissue manquante dans load schema")
            
            logger.info("   fact_jiraissue: OK")
            
            # Vérifier les tables dimension
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = 'load' AND table_name LIKE 'dim_%'
            """)
            
            dim_count = cursor.fetchone()[0]
            logger.info(f"   Tables dimension: {dim_count} trouvées")
            
            if dim_count < 50:
                logger.warning(f"   Seulement {dim_count} tables dimension (attendu: 52)")
            
            # Vérifier les colonnes FK dans fact_jiraissue
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.columns 
                WHERE table_schema = 'load' 
                AND table_name = 'fact_jiraissue'
                AND column_name LIKE '%_id'
            """)
            
            fk_cols = cursor.fetchone()[0]
            logger.info(f"   Colonnes FK dans fact_jiraissue: {fk_cols}")
            
            return True
            
        except Exception as e:
            logger.error(f"Prérequis manquants: {e}")
            return False
        finally:
            dw_conn.close()
    
    def create_all_foreign_keys(self):
        """Créer toutes les clés étrangères"""
        logger.info("\nCREATION DES CLES ETRANGERES")
        logger.info("=" * 70)

        for i, table_name in enumerate(self.all_critical_tables, 1):
            logger.info(f"[{i:2d}/{len(self.all_critical_tables)}] FK pour {table_name}")

            # Chaque FK dans sa propre transaction pour éviter les rollbacks
            dw_conn = self.connect_dw()
            cursor = dw_conn.cursor()

            try:
                # Noms des éléments
                fk_column = f"{table_name}_id"
                dim_table = f"dim_{table_name}"
                constraint_name = f"fk_fact_jiraissue_{table_name}"

                # Vérifier que la table dimension existe
                cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.tables
                    WHERE table_schema = 'load' AND table_name = %s
                """, (dim_table,))

                if cursor.fetchone()[0] == 0:
                    logger.warning(f"   Table {dim_table} manquante - SKIP")
                    self.stats['fks_failed'] += 1
                    continue

                # Vérifier que la colonne 'id' existe dans la dimension
                cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.columns
                    WHERE table_schema = 'load' AND table_name = %s AND column_name = 'id'
                """, (dim_table,))

                if cursor.fetchone()[0] == 0:
                    logger.warning(f"   Colonne 'id' manquante dans {dim_table} - SKIP")
                    self.stats['fks_failed'] += 1
                    continue

                # Vérifier que la colonne FK existe dans fact_jiraissue
                cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.columns
                    WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
                    AND column_name = %s
                """, (fk_column,))

                if cursor.fetchone()[0] == 0:
                    logger.warning(f"   Colonne {fk_column} manquante dans fact_jiraissue - SKIP")
                    self.stats['fks_failed'] += 1
                    continue

                # Supprimer la contrainte si elle existe déjà
                cursor.execute(f"""
                    ALTER TABLE load.fact_jiraissue
                    DROP CONSTRAINT IF EXISTS "{constraint_name}"
                """)

                # Créer la contrainte FK
                fk_sql = f'''
                    ALTER TABLE load.fact_jiraissue
                    ADD CONSTRAINT "{constraint_name}"
                    FOREIGN KEY ("{fk_column}")
                    REFERENCES load."{dim_table}"("id")
                    ON DELETE SET NULL
                    ON UPDATE CASCADE
                '''

                cursor.execute(fk_sql)
                dw_conn.commit()  # Commit immédiat pour cette FK
                self.stats['fks_created'] += 1

                logger.info(f"   FK {fk_column} -> {dim_table}.id")

            except Exception as e:
                error_msg = f"{table_name}: {str(e)[:100]}"
                logger.warning(f"   ERREUR: {error_msg}")
                self.stats['errors'].append(error_msg)
                self.stats['fks_failed'] += 1
                dw_conn.rollback()
            finally:
                dw_conn.close()

        logger.info(f"\nCREATION FK TERMINEE!")
    
    def verify_foreign_keys(self):
        """Vérifier les clés étrangères créées"""
        logger.info("\nVERIFICATION DES CLES ETRANGERES")
        logger.info("=" * 60)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Lister toutes les FK de fact_jiraissue
            cursor.execute("""
                SELECT 
                    tc.constraint_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                    ON ccu.constraint_name = tc.constraint_name
                    AND ccu.table_schema = tc.table_schema
                WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND tc.table_schema = 'load'
                    AND tc.table_name = 'fact_jiraissue'
                ORDER BY tc.constraint_name
            """)
            
            fks = cursor.fetchall()
            
            logger.info(f"Clés étrangères trouvées: {len(fks)}")
            
            for constraint_name, fk_col, ref_table, ref_col in fks[:10]:
                logger.info(f"   {fk_col} -> {ref_table}.{ref_col}")
            
            if len(fks) > 10:
                logger.info(f"   ... et {len(fks) - 10} autres FK")
            
            return len(fks)
            
        finally:
            dw_conn.close()

def main():
    """Point d'entrée principal"""
    print("CREATION DES CLES ETRANGERES - STAR SCHEMA")
    print("=" * 60)
    print("52 clés étrangères: fact_jiraissue -> dimensions")
    print("Architecture star schema complète")
    print("=" * 60)
    
    creator = ForeignKeyCreator()
    
    try:
        # Vérifier les prérequis
        if not creator.verify_prerequisites():
            logger.error("Prérequis manquants - ARRET")
            return 1
        
        # Créer toutes les FK
        creator.create_all_foreign_keys()
        
        # Vérifier les FK créées
        total_fks = creator.verify_foreign_keys()
        
        # Statistiques finales
        duration = (datetime.now() - creator.stats['start_time']).total_seconds()
        
        logger.info(f"\nCREATION FK TERMINEE!")
        logger.info(f"Durée: {duration:.2f} secondes")
        logger.info(f"FK créées: {creator.stats['fks_created']}")
        logger.info(f"FK échouées: {creator.stats['fks_failed']}")
        logger.info(f"Total FK actives: {total_fks}")
        logger.info(f"Erreurs: {len(creator.stats['errors'])}")
        
        if creator.stats['errors']:
            logger.warning("Erreurs rencontrées:")
            for error in creator.stats['errors'][:5]:
                logger.warning(f"   - {error}")
        
        if total_fks >= 50:
            logger.info("\nSTAR SCHEMA COMPLET!")
            logger.info("Architecture fact-dimension parfaite")
            logger.info("Prêt pour les analytics Jira")
        else:
            logger.warning(f"\nSTAR SCHEMA PARTIEL: {total_fks}/52 FK")
        
    except Exception as e:
        logger.error(f"Erreur fatale: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
