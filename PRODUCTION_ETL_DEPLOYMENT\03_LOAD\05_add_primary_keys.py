#!/usr/bin/env python3
"""
🔑 AJOUT DES CLÉS PRIMAIRES - DIMENSION TABLES
Ajout des clés primaires sur la colonne 'id' de toutes les tables dimension

PROBLÈME IDENTIFIÉ:
- Les tables dimension n'ont pas de clé primaire sur 'id'
- Les FK ne peuvent pas être créées sans PK sur les tables référencées

SOLUTION:
- Ajouter PRIMARY KEY sur 'id' pour toutes les tables dim_*
- Prérequis pour la création des FK

⚠️ PRÉREQUIS: Tables dimension créées dans le schéma load
"""

import psycopg2
import logging
from datetime import datetime

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'add_primary_keys_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PrimaryKeyAdder:
    """
    Ajout des clés primaires sur toutes les tables dimension
    """
    
    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        self.stats = {
            'start_time': datetime.now(),
            'pks_added': 0,
            'pks_failed': 0,
            'errors': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f"Erreur connexion DW: {e}")
            raise
    
    def get_dimension_tables(self):
        """Récupérer toutes les tables dimension"""
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'load' 
                AND table_name LIKE 'dim_%'
                ORDER BY table_name
            """)
            
            tables = [row[0] for row in cursor.fetchall()]
            return tables
            
        finally:
            dw_conn.close()
    
    def check_id_column_exists(self, table_name):
        """Vérifier si la colonne 'id' existe dans la table"""
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = 'load' 
                AND table_name = %s 
                AND column_name = 'id'
            """, (table_name,))
            
            return cursor.fetchone()[0] > 0
            
        finally:
            dw_conn.close()
    
    def check_primary_key_exists(self, table_name):
        """Vérifier si une clé primaire existe déjà"""
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.table_constraints 
                WHERE table_schema = 'load' 
                AND table_name = %s 
                AND constraint_type = 'PRIMARY KEY'
            """, (table_name,))
            
            return cursor.fetchone()[0] > 0
            
        finally:
            dw_conn.close()
    
    def add_primary_key(self, table_name):
        """Ajouter une clé primaire sur la colonne 'id'"""
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Nom de la contrainte PK
            pk_constraint_name = f"pk_{table_name}_id"
            
            # Supprimer la contrainte PK si elle existe
            cursor.execute(f"""
                ALTER TABLE load."{table_name}" 
                DROP CONSTRAINT IF EXISTS "{pk_constraint_name}"
            """)
            
            # Ajouter la contrainte PK
            pk_sql = f'''
                ALTER TABLE load."{table_name}"
                ADD CONSTRAINT "{pk_constraint_name}"
                PRIMARY KEY ("id")
            '''
            
            cursor.execute(pk_sql)
            dw_conn.commit()
            
            return True
            
        except Exception as e:
            dw_conn.rollback()
            raise e
        finally:
            dw_conn.close()
    
    def add_all_primary_keys(self):
        """Ajouter les clés primaires à toutes les tables dimension"""
        logger.info("AJOUT DES CLES PRIMAIRES")
        logger.info("=" * 60)
        
        # Récupérer toutes les tables dimension
        dim_tables = self.get_dimension_tables()
        logger.info(f"Tables dimension trouvées: {len(dim_tables)}")
        
        for i, table_name in enumerate(dim_tables, 1):
            logger.info(f"[{i:2d}/{len(dim_tables)}] PK pour {table_name}")
            
            try:
                # Vérifier que la colonne 'id' existe
                if not self.check_id_column_exists(table_name):
                    logger.warning(f"   Colonne 'id' manquante dans {table_name} - SKIP")
                    self.stats['pks_failed'] += 1
                    continue
                
                # Vérifier si PK existe déjà
                if self.check_primary_key_exists(table_name):
                    logger.info(f"   PK existe déjà dans {table_name} - SKIP")
                    continue
                
                # Ajouter la clé primaire
                self.add_primary_key(table_name)
                self.stats['pks_added'] += 1
                
                logger.info(f"   PK ajoutée: {table_name}.id")
                
            except Exception as e:
                error_msg = f"{table_name}: {str(e)[:100]}"
                logger.warning(f"   ERREUR: {error_msg}")
                self.stats['errors'].append(error_msg)
                self.stats['pks_failed'] += 1
                continue
        
        logger.info(f"\nAJOUT PK TERMINE!")
    
    def verify_primary_keys(self):
        """Vérifier les clés primaires créées"""
        logger.info("\nVERIFICATION DES CLES PRIMAIRES")
        logger.info("=" * 60)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Lister toutes les PK des tables dimension
            cursor.execute("""
                SELECT 
                    tc.table_name,
                    tc.constraint_name,
                    kcu.column_name
                FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                WHERE tc.constraint_type = 'PRIMARY KEY'
                    AND tc.table_schema = 'load'
                    AND tc.table_name LIKE 'dim_%'
                ORDER BY tc.table_name
            """)
            
            pks = cursor.fetchall()
            
            logger.info(f"Clés primaires trouvées: {len(pks)}")
            
            for table_name, constraint_name, column_name in pks[:10]:
                logger.info(f"   {table_name}.{column_name}")
            
            if len(pks) > 10:
                logger.info(f"   ... et {len(pks) - 10} autres PK")
            
            return len(pks)
            
        finally:
            dw_conn.close()

def main():
    """Point d'entrée principal"""
    print("AJOUT DES CLES PRIMAIRES - DIMENSION TABLES")
    print("=" * 60)
    print("Clés primaires sur 'id' pour toutes les tables dim_*")
    print("Prérequis pour la création des FK")
    print("=" * 60)
    
    adder = PrimaryKeyAdder()
    
    try:
        # Ajouter toutes les PK
        adder.add_all_primary_keys()
        
        # Vérifier les PK créées
        total_pks = adder.verify_primary_keys()
        
        # Statistiques finales
        duration = (datetime.now() - adder.stats['start_time']).total_seconds()
        
        logger.info(f"\nAJOUT PK TERMINE!")
        logger.info(f"Durée: {duration:.2f} secondes")
        logger.info(f"PK ajoutées: {adder.stats['pks_added']}")
        logger.info(f"PK échouées: {adder.stats['pks_failed']}")
        logger.info(f"Total PK actives: {total_pks}")
        logger.info(f"Erreurs: {len(adder.stats['errors'])}")
        
        if adder.stats['errors']:
            logger.warning("Erreurs rencontrées:")
            for error in adder.stats['errors'][:5]:
                logger.warning(f"   - {error}")
        
        if total_pks >= 50:
            logger.info("\nCLES PRIMAIRES COMPLETES!")
            logger.info("Prêt pour la création des FK")
            logger.info("Prochaine étape: python 04_create_foreign_keys.py")
        else:
            logger.warning(f"\nCLES PRIMAIRES PARTIELLES: {total_pks}/52 PK")
        
    except Exception as e:
        logger.error(f"Erreur fatale: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
