2025-06-08 14:22:48,305 - INFO - ======================================================================
2025-06-08 14:22:50,611 - INFO - ======================================================================
2025-06-08 14:22:50,632 - ERROR - SQL: 
                INSERT INTO load.fact_jiraissue (
                    instance_id,
                    jiraissue_id,
                    project_id,
                    component_id,
                    projectversion_id,
                    customfield_id,
                    customfieldvalue_id,
                    worklog_id,
                    fileattachment_id,
                    issuelink_id,
                    issuelinktype_id,
                    label_id,
                    nodeassociation_id,
                    cwd_user_id,
                    cwd_group_id,
                    cwd_membership_id,
                    cwd_user_attributes_id,
                    userassociation_id,
                    projectroleactor_id,
                    app_user_id,
                    cwd_directory_id,
                    jiraworkflows_id,
                    workflowscheme_id,
                    workflowschemeentity_id,
                    os_currentstep_id,
                    os_wfentry_id,
                    fieldconfiguration_id,
                    fieldconfigscheme_id,
                    permissionscheme_id,
                    schemepermissions_id,
                    fieldscreen_id,
                    fieldscreentab_id,
                    changegroup_id,
                    changeitem_id,
                    jiraaction_id,
                    pluginversion_id,
                    managedconfigurationitem_id,
                    issuestatus_id,
                    priority_id,
                    resolution_id,
                    issuetype_id,
                    projectrole_id,
                    projectcategory_id,
                    AO_4B00E6_SR_USER_PROP_id,
                    AO_4B00E6_STASH_SETTINGS_id,
                    AO_4B00E6_UPGRADE_BACKUP_id,
                    AO_786AC3_SQL_FAVOURITE_id,
                    AO_60DB71_RAPIDVIEW_id,
                    AO_60DB71_SPRINT_id,
                    AO_60DB71_ISSUERANKING_id,
                    AO_C77861_AUDIT_ENTITY_id,
                    AO_C77861_AUDIT_ACTION_CACHE_id,
                    AO_C77861_AUDIT_CATEGORY_CACHE_id
                )
                SELECT DISTINCT
                    j.instance_id,
                    j.id,                    -- jiraissue_id
                    j.project,               -- project_id
                    NULL::BIGINT,            -- component_id
                    NULL::INTEGER,           -- projectversion_id
                    NULL::BIGINT,            -- customfield_id
                    NULL::INTEGER,           -- customfieldvalue_id
                    NULL::BIGINT,            -- worklog_id
                    NULL::BIGINT,            -- fileattachment_id
                    NULL::BIGINT,            -- issuelink_id
                    NULL::BIGINT,            -- issuelinktype_id
                    NULL::BIGINT,            -- label_id
                    NULL::BIGINT,            -- nodeassociation_id
                    j.assignee,              -- cwd_user_id
                    NULL::BIGINT,            -- cwd_group_id
                    NULL::BIGINT,            -- cwd_membership_id
                    NULL::BIGINT,            -- cwd_user_attributes_id
                    NULL::BIGINT,            -- userassociation_id
                    NULL::BIGINT,            -- projectroleactor_id
                    NULL::BIGINT,            -- app_user_id
                    NULL::BIGINT,            -- cwd_directory_id
                    j.workflow_id,           -- jiraworkflows_id
                    NULL::BIGINT,            -- workflowscheme_id
                    NULL::BIGINT,            -- workflowschemeentity_id
                    NULL::BIGINT,            -- os_currentstep_id
                    NULL::BIGINT,            -- os_wfentry_id
                    NULL::BIGINT,            -- fieldconfiguration_id
                    NULL::BIGINT,            -- fieldconfigscheme_id
                    NULL::BIGINT,            -- permissionscheme_id
                    NULL::BIGINT,            -- schemepermissions_id
                    NULL::BIGINT,            -- fieldscreen_id
                    NULL::BIGINT,            -- fieldscreentab_id
                    NULL::BIGINT,            -- changegroup_id
                    NULL::BIGINT,            -- changeitem_id
                    NULL::BIGINT,            -- jiraaction_id
                    NULL::INTEGER,           -- pluginversion_id
                    NULL::BIGINT,            -- managedconfigurationitem_id
                    j.issuestatus,           -- issuestatus_id
                    j.priority,              -- priority_id
                    j.resolution,            -- resolution_id
                    j.issuetype,             -- issuetype_id
                    NULL::BIGINT,            -- projectrole_id
                    NULL::BIGINT,            -- projectcategory_id
                    NULL::INTEGER,           -- AO_4B00E6_SR_USER_PROP_id
                    NULL::INTEGER,           -- AO_4B00E6_STASH_SETTINGS_id
                    NULL::INTEGER,           -- AO_4B00E6_UPGRADE_BACKUP_id
                    NULL::INTEGER,           -- AO_786AC3_SQL_FAVOURITE_id
                    NULL::INTEGER,           -- AO_60DB71_RAPIDVIEW_id
                    NULL::INTEGER,           -- AO_60DB71_SPRINT_id
                    NULL::INTEGER,           -- AO_60DB71_ISSUERANKING_id
                    NULL::INTEGER,           -- AO_C77861_AUDIT_ENTITY_id
                    NULL::INTEGER,           -- AO_C77861_AUDIT_ACTION_CACHE_id
                    NULL::INTEGER            -- AO_C77861_AUDIT_CATEGORY_CACHE_id
                FROM load.dim_jiraissue j
                WHERE j.id IS NOT NULL
            
