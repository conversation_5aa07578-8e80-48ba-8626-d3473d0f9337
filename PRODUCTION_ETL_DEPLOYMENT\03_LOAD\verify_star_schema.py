#!/usr/bin/env python3
"""
Verification du Star Schema LOAD
Vérifie que notre star schema parfait est bien créé
"""

import psycopg2
import sys

def verify_star_schema():
    """Vérifier le star schema"""
    try:
        # Connexion à la base de données
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            database='aya',
            user='jirauser',
            password='mypassword'
        )
        cursor = conn.cursor()
        
        print("=" * 60)
        print("🌟 VERIFICATION STAR SCHEMA LOAD")
        print("=" * 60)
        
        # Compter les tables par type
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'load' AND table_name LIKE 'dim_%'
        """)
        dim_count = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'load' AND table_name LIKE 'fact_%'
        """)
        fact_count = cursor.fetchone()[0]
        
        print(f"🔷 Tables dimension: {dim_count}")
        print(f"📊 Tables fact: {fact_count}")
        print(f"⭐ Total star schema: {dim_count + fact_count}")
        print()
        
        # Vérifier la structure de la table fact
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.columns 
            WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
        """)
        fact_columns = cursor.fetchone()[0]
        print(f"🔗 Colonnes fact_jiraissue: {fact_columns}")
        
        # Vérifier les données de la table fact
        cursor.execute("SELECT COUNT(*) FROM load.fact_jiraissue")
        fact_records = cursor.fetchone()[0]
        print(f"📊 Enregistrements fact_jiraissue: {fact_records:,}")
        print()
        
        # Vérifier quelques tables dimension importantes
        important_dims = ['dim_jiraissue', 'dim_project', 'dim_cwd_user', 'dim_component']
        print("🔷 PRINCIPALES TABLES DIMENSION:")
        for dim in important_dims:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM load.{dim}")
                count = cursor.fetchone()[0]
                print(f"   {dim}: {count:,} enregistrements")
            except Exception as e:
                print(f"   {dim}: ERREUR - {e}")
        
        print()
        
        # Vérifier les colonnes FK dans fact_jiraissue
        cursor.execute("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
            AND column_name LIKE '%_id'
            ORDER BY column_name
        """)
        fk_columns = cursor.fetchall()
        print(f"🔗 COLONNES FK DANS FACT_JIRAISSUE: {len(fk_columns)}")
        for i, (col,) in enumerate(fk_columns[:10], 1):  # Afficher les 10 premières
            print(f"   {i:2d}. {col}")
        if len(fk_columns) > 10:
            print(f"   ... et {len(fk_columns) - 10} autres colonnes FK")
        
        print()
        
        # Résumé final
        if dim_count == 52 and fact_count == 1:
            print("✅ STAR SCHEMA PARFAIT!")
            print("   52 tables dimension + 1 table fact")
            print("   Structure exactement comme demandée")
            success = True
        else:
            print("⚠️ STRUCTURE INATTENDUE")
            print(f"   Attendu: 52 dim + 1 fact")
            print(f"   Trouvé: {dim_count} dim + {fact_count} fact")
            success = False
        
        conn.close()
        return success
        
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        return False

if __name__ == "__main__":
    success = verify_star_schema()
    sys.exit(0 if success else 1)
