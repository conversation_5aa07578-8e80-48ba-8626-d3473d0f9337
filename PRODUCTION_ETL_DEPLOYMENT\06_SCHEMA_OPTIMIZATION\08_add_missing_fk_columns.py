#!/usr/bin/env python3
"""
🔧 ADD MISSING FK COLUMNS TO FACT_ISSUES
Add all missing FK columns needed for 39 FK relationships
"""

import psycopg2
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_dw():
    return psycopg2.connect(
        host='localhost',
        database='aya',
        user='jirauser',
        password='mypassword'
    )

def add_missing_fk_columns():
    """Add missing FK columns to fact_issues table"""
    logger.info("🔧 ADDING MISSING FK COLUMNS TO FACT_ISSUES")
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    # Define all FK columns needed (based on dimension tables)
    missing_fk_columns = [
        # Missing FK columns to add:
        "dim_agile_id INTEGER",
        "dim_ao_4b00e6_sr_user_prop_id INTEGER", 
        "dim_ao_4b00e6_stash_settings_id INTEGER",
        "dim_app_user_id INTEGER",
        "attachment_id BIGINT",  # for dim_attachments
        "changegroup_id BIGINT",  # for dim_changegroups
        "dim_changeitem_id INTEGER",
        "customfield_id BIGINT",  # for dim_customfields
        "dim_customfieldvalue_id INTEGER",
        "dim_cwd_directory_id INTEGER",
        "dim_cwd_group_id INTEGER",
        "dim_cwd_membership_id INTEGER",
        "dim_cwd_user_attributes_id INTEGER",
        "dim_data_quality_id INTEGER",
        "date_id INTEGER",  # for dim_dates
        "dim_field_configs_id INTEGER",
        "dim_issuelink_id INTEGER",
        "dim_issuelinktype_id INTEGER",
        "dim_issues_metadata_id INTEGER",
        "dim_jiraaction_id INTEGER",
        "dim_label_id INTEGER",
        "dim_managedconfigurationitem_id INTEGER",
        "dim_nodeassociation_id INTEGER",
        "dim_os_currentstep_id INTEGER",
        "dim_os_wfentry_id INTEGER",
        "dim_permissions_id INTEGER",
        "dim_pluginversion_id INTEGER",
        "dim_projectcategory_id INTEGER",
        "dim_projectroleactor_id INTEGER",
        "dim_projectversion_id INTEGER",
        "dim_screens_id INTEGER",
        "dim_userassociation_id INTEGER",
        "dim_workflow_schemes_id INTEGER",
        "worklog_id BIGINT"  # for dim_worklogs
    ]
    
    success_count = 0
    
    for column_def in missing_fk_columns:
        try:
            # Check if column already exists
            column_name = column_def.split()[0]
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_schema = 'load' 
                AND table_name = 'fact_issues' 
                AND column_name = %s
            """, (column_name,))
            
            if cursor.fetchone():
                logger.info(f"   ✅ Column {column_name} already exists")
                continue
            
            # Add the column
            alter_sql = f"ALTER TABLE load.fact_issues ADD COLUMN {column_def}"
            cursor.execute(alter_sql)
            conn.commit()
            
            logger.info(f"   ✅ Added column: {column_name}")
            success_count += 1
            
        except Exception as e:
            logger.error(f"   ❌ Error adding {column_name}: {e}")
            conn.rollback()
    
    cursor.close()
    conn.close()
    
    logger.info(f"📊 ADDED {success_count} FK COLUMNS")
    return success_count > 0

def main():
    """Main function"""
    logger.info("🔧 ADD MISSING FK COLUMNS FOR 39 FK RELATIONSHIPS")
    logger.info("="*70)
    
    start_time = datetime.now()
    
    success = add_missing_fk_columns()
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    logger.info(f"\n🎉 FK COLUMNS ADDITION COMPLETED!")
    logger.info(f"   Duration: {duration}")
    
    if success:
        logger.info(f"   ✅ SUCCESS: FK columns added successfully")
        return 0
    else:
        logger.info(f"   ❌ FAILED: Error adding FK columns")
        return 1

if __name__ == "__main__":
    exit(main())
