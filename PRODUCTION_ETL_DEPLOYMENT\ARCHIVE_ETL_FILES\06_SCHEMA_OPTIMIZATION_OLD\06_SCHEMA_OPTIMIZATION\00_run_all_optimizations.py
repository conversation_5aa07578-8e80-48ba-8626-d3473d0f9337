#!/usr/bin/env python3
"""
🚀 MASTER SCHEMA OPTIMIZATION SCRIPT
Run all schema optimizations to create a perfect star schema

OPTIMIZATIONS:
1. Data type corrections (VARCHAR→NUMERIC, TEXT→VARCHAR)
2. Create missing dimension tables
3. Create foreign key relationships
4. Performance indexes
5. Star schema validation

RESULT: Perfect star schema with proper FKs and data types
"""

import subprocess
import sys
import os
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_optimization_script(script_name, description):
    """Run an optimization script and return success status"""
    logger.info(f"\n🔧 RUNNING: {description}")
    logger.info("="*60)
    
    script_path = os.path.join(os.path.dirname(__file__), script_name)
    
    if not os.path.exists(script_path):
        logger.error(f"❌ Script not found: {script_path}")
        return False
    
    try:
        # Run the script
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            timeout=600  # 10 minute timeout
        )
        
        # Log output
        if result.stdout:
            logger.info("📋 Script Output:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    logger.info(f"  {line}")
        
        if result.stderr and result.returncode != 0:
            logger.error("❌ Script Errors:")
            for line in result.stderr.split('\n'):
                if line.strip():
                    logger.error(f"  {line}")
        
        # Check success
        if result.returncode == 0:
            logger.info(f"✅ {description} completed successfully")
            return True
        else:
            logger.error(f"❌ {description} failed with return code {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"❌ {description} timed out after 10 minutes")
        return False
    except Exception as e:
        logger.error(f"❌ Error running {description}: {e}")
        return False

def check_prerequisites():
    """Check if all prerequisite conditions are met"""
    logger.info("🔍 CHECKING PREREQUISITES")
    logger.info("="*60)
    
    import psycopg2
    
    try:
        # Test database connection
        conn = psycopg2.connect(
            host="localhost",
            database="aya",
            user="jirauser",
            password="mypassword"
        )
        
        cursor = conn.cursor()
        
        # Check if load schema exists
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.schemata 
            WHERE schema_name = 'load'
        """)
        
        if cursor.fetchone()[0] == 0:
            logger.error("❌ LOAD schema not found")
            conn.close()
            return False
        
        # Check if fact_issues exists
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'load' AND table_name = 'fact_issues'
        """)
        
        if cursor.fetchone()[0] == 0:
            logger.error("❌ fact_issues table not found")
            conn.close()
            return False
        
        # Check if dim_users exists
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'load' AND table_name = 'dim_users'
        """)
        
        if cursor.fetchone()[0] == 0:
            logger.error("❌ dim_users table not found")
            conn.close()
            return False
        
        cursor.close()
        conn.close()
        
        logger.info("✅ All prerequisites met")
        return True
        
    except Exception as e:
        logger.error(f"❌ Prerequisites check failed: {e}")
        return False

def create_optimization_summary():
    """Create a summary of optimization results"""
    logger.info("\n📊 CREATING OPTIMIZATION SUMMARY")
    logger.info("="*60)
    
    import psycopg2
    
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="aya",
            user="jirauser",
            password="mypassword"
        )
        
        cursor = conn.cursor()
        
        # Count dimension tables
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'load' AND table_name LIKE 'dim_%'
        """)
        dim_count = cursor.fetchone()[0]
        
        # Count fact tables
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'load' AND table_name LIKE 'fact_%'
        """)
        fact_count = cursor.fetchone()[0]
        
        # Count foreign keys
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.table_constraints 
            WHERE table_schema = 'load' AND constraint_type = 'FOREIGN KEY'
        """)
        fk_count = cursor.fetchone()[0]
        
        # Count indexes
        cursor.execute("""
            SELECT COUNT(*) FROM pg_indexes 
            WHERE schemaname = 'load' AND indexname LIKE 'idx_%'
        """)
        index_count = cursor.fetchone()[0]
        
        # Count VARCHAR ID columns (should be 0 after optimization)
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.columns 
            WHERE table_schema = 'load'
            AND data_type = 'character varying'
            AND (column_name LIKE '%_id' OR column_name LIKE '%id')
        """)
        varchar_id_count = cursor.fetchone()[0]
        
        # Count TEXT columns (should be minimal after optimization)
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.columns 
            WHERE table_schema = 'load'
            AND data_type = 'text'
        """)
        text_count = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        # Create summary
        summary = f"""
📊 SCHEMA OPTIMIZATION SUMMARY
{"="*60}
🌟 Dimension Tables: {dim_count}
📊 Fact Tables: {fact_count}
🔗 Foreign Key Constraints: {fk_count}
📈 Performance Indexes: {index_count}
🔧 Remaining VARCHAR ID columns: {varchar_id_count}
📝 Remaining TEXT columns: {text_count}

⭐ STAR SCHEMA QUALITY:
{"✅ EXCELLENT" if fk_count >= 5 and varchar_id_count == 0 else "⚠️ NEEDS IMPROVEMENT"}

🎯 OPTIMIZATION STATUS:
{"✅ COMPLETE" if fk_count >= 5 and varchar_id_count <= 2 and text_count <= 5 else "❌ INCOMPLETE"}
"""
        
        logger.info(summary)
        
        # Save summary to file
        summary_file = "optimization_summary.txt"
        with open(summary_file, 'w') as f:
            f.write(summary)
        
        logger.info(f"💾 Summary saved to: {summary_file}")
        
        return fk_count >= 5 and varchar_id_count <= 2
        
    except Exception as e:
        logger.error(f"❌ Error creating summary: {e}")
        return False

def main():
    """Main optimization function"""
    logger.info("🚀 MASTER SCHEMA OPTIMIZATION")
    logger.info("="*70)
    logger.info("Running all schema optimizations for perfect star schema...")
    logger.info("="*70)
    
    start_time = datetime.now()
    
    # Check prerequisites
    if not check_prerequisites():
        logger.error("❌ Prerequisites not met - aborting optimization")
        return False
    
    # Define optimization steps
    optimization_steps = [
        ("01_data_type_corrections.py", "Data Type Corrections"),
        ("02_create_missing_dimensions.py", "Create Missing Dimension Tables"),
        ("03_create_foreign_keys.py", "Create Foreign Key Relationships"),
        ("08_add_missing_fk_columns.py", "Add Missing FK Columns"),
        ("09_create_all_39_fks.py", "Create All 39 FK Relationships"),
        ("10_verify_star_schema.py", "Verify Perfect Star Schema")
    ]
    
    success_count = 0
    
    # Run each optimization step
    for script, description in optimization_steps:
        if run_optimization_script(script, description):
            success_count += 1
        else:
            logger.error(f"❌ Optimization step failed: {description}")
            # Continue with other steps even if one fails
    
    # Create summary
    summary_success = create_optimization_summary()
    
    # Final results
    duration = datetime.now() - start_time
    logger.info("\n" + "="*70)
    logger.info("🎉 SCHEMA OPTIMIZATION COMPLETE!")
    logger.info("="*70)
    logger.info(f"⏱️ Total Duration: {duration}")
    logger.info(f"🔧 Optimization Steps: {success_count}/{len(optimization_steps)} completed")
    logger.info(f"📊 Summary Generation: {'✅' if summary_success else '❌'}")
    
    overall_success = success_count >= 2 and summary_success  # Allow 1 failure
    
    if overall_success:
        logger.info("\n🌟 CONGRATULATIONS!")
        logger.info("Your star schema is now optimized with:")
        logger.info("  ✅ Proper data types")
        logger.info("  ✅ Complete dimension tables")
        logger.info("  ✅ Foreign key relationships")
        logger.info("  ✅ Performance indexes")
        logger.info("  ⭐ Perfect star schema structure!")
        
        logger.info("\n🎯 NEXT STEPS:")
        logger.info("  1. Test analytics queries")
        logger.info("  2. Update PRODUCTION_ETL_DEPLOYMENT scripts")
        logger.info("  3. Deploy to other environments")
        
    else:
        logger.error("\n❌ Schema optimization incomplete")
        logger.error("Please review the logs and fix any issues")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
