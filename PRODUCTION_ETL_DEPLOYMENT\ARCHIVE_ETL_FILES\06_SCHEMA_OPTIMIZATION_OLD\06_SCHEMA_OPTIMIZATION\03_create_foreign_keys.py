#!/usr/bin/env python3
"""
🔗 CREATE FOREIGN KEY RELATIONSHIPS
Create foreign key constraints to make the star schema look like a proper star

CREATES FK RELATIONSHIPS:
1. fact_issues → dim_priorities
2. fact_issues → dim_statuses  
3. fact_issues → dim_resolutions
4. fact_issues → dim_issue_types
5. fact_issues → dim_users (for reporter, assignee, creator)
6. fact_issues → dim_projects
7. fact_issues → dim_components
8. Other fact tables → dimension tables
"""

import psycopg2
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_to_database(dbname="aya"):
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database=dbname,
            user="jirauser",
            password="mypassword"
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to {dbname}: {e}")
        return None

def add_lookup_columns_to_fact_issues():
    """Add lookup columns to fact_issues for FK relationships"""
    logger.info("🔧 ADDING LOOKUP COLUMNS TO fact_issues")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        # Add FK columns if they don't exist
        new_columns = [
            ("priority_id", "INTEGER"),
            ("status_id", "INTEGER"),
            ("resolution_id", "INTEGER"),
            ("issue_type_id", "INTEGER")
        ]
        
        for col_name, col_type in new_columns:
            # Check if column exists
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.columns 
                WHERE table_schema = 'load' AND table_name = 'fact_issues' AND column_name = %s
            """, (col_name,))
            
            if cursor.fetchone()[0] == 0:
                cursor.execute(f'ALTER TABLE load.fact_issues ADD COLUMN {col_name} {col_type}')
                logger.info(f"  ✅ Added column: {col_name}")
            else:
                logger.info(f"  ⚠️ Column already exists: {col_name}")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info("✅ Lookup columns added successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error adding lookup columns: {e}")
        conn.rollback()
        cursor.close()
        conn.close()
        return False

def populate_lookup_columns():
    """Populate the new lookup columns with dimension IDs"""
    logger.info("\n🔧 POPULATING LOOKUP COLUMNS")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        # Update priority_id
        cursor.execute('''
            UPDATE load.fact_issues 
            SET priority_id = dp.id
            FROM load.dim_priorities dp
            WHERE fact_issues.priority = dp.priority_name
        ''')
        priority_updated = cursor.rowcount
        logger.info(f"  ✅ Updated priority_id: {priority_updated} rows")
        
        # Update status_id
        cursor.execute('''
            UPDATE load.fact_issues 
            SET status_id = ds.id
            FROM load.dim_statuses ds
            WHERE fact_issues.status = ds.status_name
        ''')
        status_updated = cursor.rowcount
        logger.info(f"  ✅ Updated status_id: {status_updated} rows")
        
        # Update resolution_id
        cursor.execute('''
            UPDATE load.fact_issues 
            SET resolution_id = dr.id
            FROM load.dim_resolutions dr
            WHERE COALESCE(fact_issues.resolution, 'Unresolved') = dr.resolution_name
        ''')
        resolution_updated = cursor.rowcount
        logger.info(f"  ✅ Updated resolution_id: {resolution_updated} rows")
        
        # Update issue_type_id
        cursor.execute('''
            UPDATE load.fact_issues 
            SET issue_type_id = dit.id
            FROM load.dim_issue_types dit
            WHERE fact_issues.issue_type = dit.issue_type_name
        ''')
        issue_type_updated = cursor.rowcount
        logger.info(f"  ✅ Updated issue_type_id: {issue_type_updated} rows")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info("✅ Lookup columns populated successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error populating lookup columns: {e}")
        conn.rollback()
        cursor.close()
        conn.close()
        return False

def create_foreign_key_constraints():
    """Create foreign key constraints"""
    logger.info("\n🔗 CREATING FOREIGN KEY CONSTRAINTS")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Define foreign key relationships
    foreign_keys = [
        # fact_issues foreign keys
        ("fact_issues", "priority_id", "dim_priorities", "id", "fk_fact_issues_priority"),
        ("fact_issues", "status_id", "dim_statuses", "id", "fk_fact_issues_status"),
        ("fact_issues", "resolution_id", "dim_resolutions", "id", "fk_fact_issues_resolution"),
        ("fact_issues", "issue_type_id", "dim_issue_types", "id", "fk_fact_issues_issue_type"),
        ("fact_issues", "reporter_id", "dim_users", "id", "fk_fact_issues_reporter"),
        ("fact_issues", "assignee_id", "dim_users", "id", "fk_fact_issues_assignee"),
        ("fact_issues", "creator_id", "dim_users", "id", "fk_fact_issues_creator"),
        ("fact_issues", "project_id", "dim_projects", "id", "fk_fact_issues_project"),
        ("fact_issues", "component_id", "dim_components", "id", "fk_fact_issues_component"),
        
        # fact_worklogs foreign keys (if exists)
        ("fact_worklogs", "issue_id", "fact_issues", "issue_id", "fk_fact_worklogs_issue"),
        ("fact_worklogs", "author_id", "dim_users", "id", "fk_fact_worklogs_author")
    ]
    
    success_count = 0
    
    for fact_table, fact_column, dim_table, dim_column, constraint_name in foreign_keys:
        try:
            # Check if both tables exist
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = 'load' AND table_name IN (%s, %s)
            """, (fact_table, dim_table))
            
            if cursor.fetchone()[0] != 2:
                logger.warning(f"  ⚠️ Skipping {constraint_name}: Missing table(s)")
                continue
            
            # Check if columns exist
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.columns 
                WHERE table_schema = 'load' AND table_name = %s AND column_name = %s
            """, (fact_table, fact_column))
            
            fact_col_exists = cursor.fetchone()[0] > 0
            
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.columns 
                WHERE table_schema = 'load' AND table_name = %s AND column_name = %s
            """, (dim_table, dim_column))
            
            dim_col_exists = cursor.fetchone()[0] > 0
            
            if not fact_col_exists or not dim_col_exists:
                logger.warning(f"  ⚠️ Skipping {constraint_name}: Missing column(s)")
                continue
            
            # Check if constraint already exists
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.table_constraints 
                WHERE table_schema = 'load' AND constraint_name = %s
            """, (constraint_name,))
            
            if cursor.fetchone()[0] > 0:
                logger.info(f"  ⚠️ FK already exists: {constraint_name}")
                success_count += 1
                continue
            
            # Create foreign key constraint
            cursor.execute(f'''
                ALTER TABLE load."{fact_table}" 
                ADD CONSTRAINT "{constraint_name}" 
                FOREIGN KEY ("{fact_column}") 
                REFERENCES load."{dim_table}" ("{dim_column}")
                ON DELETE SET NULL
                ON UPDATE CASCADE
            ''')
            
            conn.commit()
            logger.info(f"  ✅ Created FK: {fact_table}.{fact_column} → {dim_table}.{dim_column}")
            success_count += 1
            
        except Exception as e:
            logger.error(f"  ❌ Error creating {constraint_name}: {e}")
            conn.rollback()
    
    cursor.close()
    conn.close()
    
    logger.info(f"\n📊 FOREIGN KEYS CREATED: {success_count}/{len(foreign_keys)}")
    return success_count > 0

def create_indexes_for_performance():
    """Create indexes on foreign key columns for better performance"""
    logger.info("\n📈 CREATING PERFORMANCE INDEXES")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Define indexes for FK columns
    indexes = [
        ("fact_issues", "priority_id", "idx_fact_issues_priority_id"),
        ("fact_issues", "status_id", "idx_fact_issues_status_id"),
        ("fact_issues", "resolution_id", "idx_fact_issues_resolution_id"),
        ("fact_issues", "issue_type_id", "idx_fact_issues_issue_type_id"),
        ("fact_issues", "reporter_id", "idx_fact_issues_reporter_id"),
        ("fact_issues", "assignee_id", "idx_fact_issues_assignee_id"),
        ("fact_issues", "creator_id", "idx_fact_issues_creator_id"),
        ("fact_issues", "project_id", "idx_fact_issues_project_id"),
        ("fact_issues", "component_id", "idx_fact_issues_component_id"),
        ("fact_issues", "created_date", "idx_fact_issues_created_date"),
        ("fact_issues", "updated_date", "idx_fact_issues_updated_date")
    ]
    
    success_count = 0
    
    for table, column, index_name in indexes:
        try:
            # Check if column exists
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.columns 
                WHERE table_schema = 'load' AND table_name = %s AND column_name = %s
            """, (table, column))
            
            if cursor.fetchone()[0] == 0:
                logger.warning(f"  ⚠️ Skipping index {index_name}: Column doesn't exist")
                continue
            
            # Create index
            cursor.execute(f'''
                CREATE INDEX IF NOT EXISTS "{index_name}" 
                ON load."{table}" ("{column}")
            ''')
            
            conn.commit()
            logger.info(f"  ✅ Created index: {index_name}")
            success_count += 1
            
        except Exception as e:
            logger.error(f"  ❌ Error creating index {index_name}: {e}")
            conn.rollback()
    
    cursor.close()
    conn.close()
    
    logger.info(f"\n📊 INDEXES CREATED: {success_count}/{len(indexes)}")
    return success_count > 0

def validate_star_schema():
    """Validate the star schema structure"""
    logger.info("\n⭐ VALIDATING STAR SCHEMA STRUCTURE")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Count foreign keys
    cursor.execute("""
        SELECT COUNT(*) FROM information_schema.table_constraints 
        WHERE table_schema = 'load' AND constraint_type = 'FOREIGN KEY'
    """)
    
    fk_count = cursor.fetchone()[0]
    
    # Count dimension tables
    cursor.execute("""
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'load' AND table_name LIKE 'dim_%'
    """)
    
    dim_count = cursor.fetchone()[0]
    
    # Count fact tables
    cursor.execute("""
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'load' AND table_name LIKE 'fact_%'
    """)
    
    fact_count = cursor.fetchone()[0]
    
    # Count indexes
    cursor.execute("""
        SELECT COUNT(*) FROM pg_indexes 
        WHERE schemaname = 'load' AND indexname LIKE 'idx_%'
    """)
    
    index_count = cursor.fetchone()[0]
    
    cursor.close()
    conn.close()
    
    logger.info(f"📊 Star Schema Metrics:")
    logger.info(f"  🌟 Dimension tables: {dim_count}")
    logger.info(f"  📊 Fact tables: {fact_count}")
    logger.info(f"  🔗 Foreign keys: {fk_count}")
    logger.info(f"  📈 Performance indexes: {index_count}")
    
    # Validate minimum requirements
    is_valid = (
        dim_count >= 5 and  # At least 5 dimension tables
        fact_count >= 1 and  # At least 1 fact table
        fk_count >= 5 and   # At least 5 foreign keys
        index_count >= 5    # At least 5 indexes
    )
    
    if is_valid:
        logger.info("✅ Star schema validation PASSED!")
    else:
        logger.warning("⚠️ Star schema validation needs improvement")
    
    return is_valid

def main():
    """Main foreign key creation function"""
    logger.info("🔗 CREATE FOREIGN KEY RELATIONSHIPS")
    logger.info("="*70)
    logger.info("Creating FK constraints for proper star schema...")
    logger.info("="*70)
    
    start_time = datetime.now()
    
    # Execute steps
    steps = [
        ("Add lookup columns", add_lookup_columns_to_fact_issues),
        ("Populate lookup columns", populate_lookup_columns),
        ("Create foreign keys", create_foreign_key_constraints),
        ("Create performance indexes", create_indexes_for_performance)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        logger.info(f"\n📋 {step_name}...")
        if step_func():
            success_count += 1
        else:
            logger.error(f"❌ Failed: {step_name}")
    
    # Validate star schema
    validation_success = validate_star_schema()
    
    # Summary
    duration = datetime.now() - start_time
    logger.info("\n" + "="*70)
    logger.info("📊 FOREIGN KEY CREATION SUMMARY")
    logger.info("="*70)
    logger.info(f"⏱️ Duration: {duration}")
    logger.info(f"🔗 Steps completed: {success_count}/{len(steps)}")
    logger.info(f"⭐ Star schema validation: {'✅' if validation_success else '❌'}")
    
    overall_success = success_count == len(steps) and validation_success
    
    if overall_success:
        logger.info("\n🎉 STAR SCHEMA FOREIGN KEYS CREATED SUCCESSFULLY!")
        logger.info("⭐ Your star schema now looks like a proper star!")
    else:
        logger.error("\n❌ Some foreign key creation failed")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
