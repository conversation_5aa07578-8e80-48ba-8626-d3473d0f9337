#!/usr/bin/env python3
"""
🔗 FIX DENORMALIZED FOREIGN KEYS
Handle denormalized tables properly for star schema FK relationships

DENORMALIZATION UNDERSTANDING:
- Transform tables have denormalized user columns (assignee_username, assignee_email, etc.)
- These should connect to dim_users through the original user reference columns
- Need to handle both normalized and denormalized columns properly

FIXES:
1. Identify denormalized user columns in transform tables
2. Create proper FK relationships using original user reference columns
3. <PERSON>le denormalized dimension tables correctly
4. Create bridge tables where needed for many-to-many relationships
"""

import psycopg2
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_to_database(dbname="aya"):
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database=dbname,
            user="jirauser",
            password="mypassword"
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to {dbname}: {e}")
        return None

def analyze_denormalized_structure():
    """Analyze the denormalized structure in transform and load schemas"""
    logger.info("🔍 ANALYZING DENORMALIZED STRUCTURE")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return {}
    
    cursor = conn.cursor()
    
    # Find denormalized user columns in transform schema
    cursor.execute("""
        SELECT table_name, column_name 
        FROM information_schema.columns 
        WHERE table_schema = 'transform' 
        AND (column_name LIKE '%_username' OR column_name LIKE '%_email' OR column_name LIKE '%_display_name')
        ORDER BY table_name, column_name
    """)
    
    denorm_columns = cursor.fetchall()
    
    # Group by table and base user column
    denorm_structure = {}
    
    for table, column in denorm_columns:
        if table not in denorm_structure:
            denorm_structure[table] = {}
        
        # Extract base user column name
        if column.endswith('_username'):
            base_col = column[:-9]  # Remove '_username'
        elif column.endswith('_email'):
            base_col = column[:-6]   # Remove '_email'
        elif column.endswith('_display_name'):
            base_col = column[:-13]  # Remove '_display_name'
        else:
            continue
        
        if base_col not in denorm_structure[table]:
            denorm_structure[table] = {base_col: []}
        
        denorm_structure[table][base_col].append(column)
    
    logger.info(f"📊 Found denormalized user columns in {len(denorm_structure)} tables:")
    for table, user_cols in denorm_structure.items():
        logger.info(f"  📋 {table}:")
        for base_col, denorm_cols in user_cols.items():
            logger.info(f"    🔗 {base_col} → {len(denorm_cols)} denormalized columns")
    
    cursor.close()
    conn.close()
    
    return denorm_structure

def check_original_user_columns():
    """Check which original user reference columns exist"""
    logger.info("\n🔍 CHECKING ORIGINAL USER REFERENCE COLUMNS")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return {}
    
    cursor = conn.cursor()
    
    # Check for original user columns in load schema fact tables
    cursor.execute("""
        SELECT table_name, column_name 
        FROM information_schema.columns 
        WHERE table_schema = 'load' 
        AND table_name LIKE 'fact_%'
        AND (column_name IN ('assignee', 'reporter', 'creator', 'author', 'lead', 'updateauthor')
             OR column_name LIKE '%_id')
        ORDER BY table_name, column_name
    """)
    
    user_ref_columns = cursor.fetchall()
    
    user_refs = {}
    for table, column in user_ref_columns:
        if table not in user_refs:
            user_refs[table] = []
        user_refs[table].append(column)
    
    logger.info(f"📊 Found user reference columns in {len(user_refs)} fact tables:")
    for table, columns in user_refs.items():
        logger.info(f"  📋 {table}: {', '.join(columns)}")
    
    cursor.close()
    conn.close()
    
    return user_refs

def create_user_lookup_columns():
    """Create user lookup ID columns in fact tables based on denormalized data"""
    logger.info("\n🔧 CREATING USER LOOKUP COLUMNS")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Define user columns that need ID lookups
    user_lookup_mappings = {
        'fact_issues': {
            'reporter': 'reporter_id',
            'assignee': 'assignee_id', 
            'creator': 'creator_id'
        }
    }
    
    success_count = 0
    
    for table, mappings in user_lookup_mappings.items():
        for user_col, id_col in mappings.items():
            try:
                # Check if ID column already exists
                cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.columns 
                    WHERE table_schema = 'load' AND table_name = %s AND column_name = %s
                """, (table, id_col))
                
                if cursor.fetchone()[0] == 0:
                    # Add the ID column
                    cursor.execute(f'ALTER TABLE load."{table}" ADD COLUMN "{id_col}" INTEGER')
                    logger.info(f"  ✅ Added {table}.{id_col}")
                else:
                    logger.info(f"  ⚠️ {table}.{id_col} already exists")
                
                success_count += 1
                
            except Exception as e:
                logger.error(f"  ❌ Error adding {table}.{id_col}: {e}")
                conn.rollback()
    
    conn.commit()
    cursor.close()
    conn.close()
    
    logger.info(f"\n📊 USER LOOKUP COLUMNS: {success_count} processed")
    return success_count > 0

def populate_user_lookup_ids():
    """Populate user lookup ID columns using dim_users"""
    logger.info("\n🔧 POPULATING USER LOOKUP IDs")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Define mappings from user name columns to ID columns
    user_id_mappings = [
        ('fact_issues', 'reporter', 'reporter_id'),
        ('fact_issues', 'assignee', 'assignee_id'),
        ('fact_issues', 'creator', 'creator_id')
    ]
    
    success_count = 0
    
    for table, user_col, id_col in user_id_mappings:
        try:
            # Check if both columns exist
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.columns 
                WHERE table_schema = 'load' AND table_name = %s 
                AND column_name IN (%s, %s)
            """, (table, user_col, id_col))
            
            if cursor.fetchone()[0] != 2:
                logger.warning(f"  ⚠️ Skipping {table}: Missing columns {user_col} or {id_col}")
                continue
            
            # Update ID column using dim_users lookup
            cursor.execute(f'''
                UPDATE load."{table}" 
                SET "{id_col}" = du.user_id
                FROM load.dim_users du
                WHERE load."{table}"."{user_col}" = du.user_name
            ''')
            
            updated_rows = cursor.rowcount
            conn.commit()
            
            logger.info(f"  ✅ Updated {table}.{id_col}: {updated_rows} rows")
            success_count += 1
            
        except Exception as e:
            logger.error(f"  ❌ Error updating {table}.{id_col}: {e}")
            conn.rollback()
    
    cursor.close()
    conn.close()
    
    logger.info(f"\n📊 USER ID POPULATION: {success_count} columns updated")
    return success_count > 0

def create_denormalized_foreign_keys():
    """Create foreign key constraints for denormalized tables"""
    logger.info("\n🔗 CREATING DENORMALIZED FOREIGN KEYS")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Define FK relationships for denormalized structure
    denorm_foreign_keys = [
        # User reference FKs
        ("fact_issues", "reporter_id", "dim_users", "user_id", "fk_fact_issues_reporter_denorm"),
        ("fact_issues", "assignee_id", "dim_users", "user_id", "fk_fact_issues_assignee_denorm"),
        ("fact_issues", "creator_id", "dim_users", "user_id", "fk_fact_issues_creator_denorm"),
        
        # Project and component FKs (if they exist)
        ("fact_issues", "project_id", "dim_projects", "id", "fk_fact_issues_project_denorm"),
        ("fact_issues", "component_id", "dim_components", "id", "fk_fact_issues_component_denorm"),
        
        # Metadata FKs (using our created dimension tables)
        ("fact_issues", "priority_id", "dim_priorities", "id", "fk_fact_issues_priority_denorm"),
        ("fact_issues", "status_id", "dim_statuses", "id", "fk_fact_issues_status_denorm"),
        ("fact_issues", "resolution_id", "dim_resolutions", "id", "fk_fact_issues_resolution_denorm"),
        ("fact_issues", "issue_type_id", "dim_issue_types", "id", "fk_fact_issues_issue_type_denorm")
    ]
    
    success_count = 0
    
    for fact_table, fact_column, dim_table, dim_column, constraint_name in denorm_foreign_keys:
        try:
            # Check if both tables and columns exist
            cursor.execute("""
                SELECT 
                    (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = 'load' AND table_name = %s) as fact_exists,
                    (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = 'load' AND table_name = %s) as dim_exists,
                    (SELECT COUNT(*) FROM information_schema.columns 
                     WHERE table_schema = 'load' AND table_name = %s AND column_name = %s) as fact_col_exists,
                    (SELECT COUNT(*) FROM information_schema.columns 
                     WHERE table_schema = 'load' AND table_name = %s AND column_name = %s) as dim_col_exists
            """, (fact_table, dim_table, fact_table, fact_column, dim_table, dim_column))
            
            result = cursor.fetchone()
            fact_exists, dim_exists, fact_col_exists, dim_col_exists = result
            
            if not all([fact_exists, dim_exists, fact_col_exists, dim_col_exists]):
                logger.warning(f"  ⚠️ Skipping {constraint_name}: Missing table/column")
                continue
            
            # Check if constraint already exists
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.table_constraints 
                WHERE table_schema = 'load' AND constraint_name = %s
            """, (constraint_name,))
            
            if cursor.fetchone()[0] > 0:
                logger.info(f"  ⚠️ FK already exists: {constraint_name}")
                success_count += 1
                continue
            
            # Create foreign key constraint
            cursor.execute(f'''
                ALTER TABLE load."{fact_table}" 
                ADD CONSTRAINT "{constraint_name}" 
                FOREIGN KEY ("{fact_column}") 
                REFERENCES load."{dim_table}" ("{dim_column}")
                ON DELETE SET NULL
                ON UPDATE CASCADE
            ''')
            
            conn.commit()
            logger.info(f"  ✅ Created FK: {fact_table}.{fact_column} → {dim_table}.{dim_column}")
            success_count += 1
            
        except Exception as e:
            logger.error(f"  ❌ Error creating {constraint_name}: {e}")
            conn.rollback()
    
    cursor.close()
    conn.close()
    
    logger.info(f"\n📊 DENORMALIZED FOREIGN KEYS: {success_count} created")
    return success_count > 0

def validate_denormalized_star_schema():
    """Validate the denormalized star schema structure"""
    logger.info("\n⭐ VALIDATING DENORMALIZED STAR SCHEMA")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Count foreign keys
    cursor.execute("""
        SELECT COUNT(*) FROM information_schema.table_constraints 
        WHERE table_schema = 'load' AND constraint_type = 'FOREIGN KEY'
    """)
    fk_count = cursor.fetchone()[0]
    
    # Count dimension tables
    cursor.execute("""
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'load' AND table_name LIKE 'dim_%'
    """)
    dim_count = cursor.fetchone()[0]
    
    # Count fact tables
    cursor.execute("""
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'load' AND table_name LIKE 'fact_%'
    """)
    fact_count = cursor.fetchone()[0]
    
    # Check denormalized columns in load schema
    cursor.execute("""
        SELECT COUNT(*) FROM information_schema.columns 
        WHERE table_schema = 'load' 
        AND (column_name LIKE '%_username' OR column_name LIKE '%_email' OR column_name LIKE '%_display_name')
    """)
    denorm_col_count = cursor.fetchone()[0]
    
    cursor.close()
    conn.close()
    
    logger.info(f"📊 Denormalized Star Schema Metrics:")
    logger.info(f"  🌟 Dimension tables: {dim_count}")
    logger.info(f"  📊 Fact tables: {fact_count}")
    logger.info(f"  🔗 Foreign keys: {fk_count}")
    logger.info(f"  📋 Denormalized columns: {denorm_col_count}")
    
    # Validate requirements for denormalized star schema
    is_valid = (
        dim_count >= 5 and      # At least 5 dimension tables
        fact_count >= 1 and     # At least 1 fact table
        fk_count >= 3           # At least 3 foreign keys (less than normalized due to denormalization)
    )
    
    if is_valid:
        logger.info("✅ Denormalized star schema validation PASSED!")
        logger.info("🎯 Schema properly handles denormalized user data")
    else:
        logger.warning("⚠️ Denormalized star schema validation needs improvement")
    
    return is_valid

def main():
    """Main denormalized FK creation function"""
    logger.info("🔗 FIX DENORMALIZED FOREIGN KEYS")
    logger.info("="*70)
    logger.info("Handling denormalized tables for proper star schema...")
    logger.info("="*70)
    
    start_time = datetime.now()
    
    # Step 1: Analyze denormalized structure
    denorm_structure = analyze_denormalized_structure()
    
    # Step 2: Check original user columns
    user_refs = check_original_user_columns()
    
    # Step 3: Create user lookup columns
    lookup_success = create_user_lookup_columns()
    
    # Step 4: Populate user lookup IDs
    populate_success = populate_user_lookup_ids()
    
    # Step 5: Create denormalized foreign keys
    fk_success = create_denormalized_foreign_keys()
    
    # Step 6: Validate denormalized star schema
    validation_success = validate_denormalized_star_schema()
    
    # Summary
    duration = datetime.now() - start_time
    logger.info("\n" + "="*70)
    logger.info("📊 DENORMALIZED FK CREATION SUMMARY")
    logger.info("="*70)
    logger.info(f"⏱️ Duration: {duration}")
    logger.info(f"🔍 Denormalized structure: {'✅' if denorm_structure else '❌'}")
    logger.info(f"🔧 User lookup columns: {'✅' if lookup_success else '❌'}")
    logger.info(f"📊 ID population: {'✅' if populate_success else '❌'}")
    logger.info(f"🔗 Foreign keys: {'✅' if fk_success else '❌'}")
    logger.info(f"⭐ Validation: {'✅' if validation_success else '❌'}")
    
    overall_success = lookup_success and populate_success and fk_success and validation_success
    
    if overall_success:
        logger.info("\n🎉 DENORMALIZED STAR SCHEMA FIXED SUCCESSFULLY!")
        logger.info("⭐ Your denormalized star schema now has proper FK relationships!")
    else:
        logger.error("\n❌ Some denormalized FK fixes failed")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
