#!/usr/bin/env python3
"""
🧹 CLEANUP EXTRA TABLES
Remove the extra dimension tables I created and fix FK relationships to use existing tables

PROBLEM:
- I created dim_priorities, dim_statuses, dim_resolutions, dim_issue_types
- But we should use the existing dimension tables in the load schema
- Need to get back to exactly 40 tables, not 46

SOLUTION:
1. Drop the extra tables I created
2. Fix FK relationships to use existing dimension tables
3. Ensure we have exactly 40 tables in load schema
"""

import psycopg2
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_to_database(dbname="aya"):
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database=dbname,
            user="jirauser",
            password="mypassword"
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to {dbname}: {e}")
        return None

def identify_extra_tables():
    """Identify the extra tables I created"""
    logger.info("🔍 IDENTIFYING EXTRA TABLES")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return []
    
    cursor = conn.cursor()
    
    # Get current table count
    cursor.execute("""
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'load'
    """)
    current_count = cursor.fetchone()[0]
    
    logger.info(f"📊 Current table count: {current_count} (should be 40)")
    
    # The extra tables I created
    extra_tables = [
        'dim_priorities',
        'dim_statuses', 
        'dim_resolutions',
        'dim_issue_types'
    ]
    
    # Check which ones exist
    existing_extra = []
    for table in extra_tables:
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'load' AND table_name = %s
        """, (table,))
        
        if cursor.fetchone()[0] > 0:
            existing_extra.append(table)
            logger.info(f"  ❌ Found extra table: {table}")
    
    # Check for user reference views
    user_views = ['dim_reporters', 'dim_assignees', 'dim_creators']
    for view in user_views:
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.views 
            WHERE table_schema = 'load' AND table_name = %s
        """, (view,))
        
        if cursor.fetchone()[0] > 0:
            existing_extra.append(view)
            logger.info(f"  ❌ Found extra view: {view}")
    
    cursor.close()
    conn.close()
    
    logger.info(f"📊 Found {len(existing_extra)} extra tables/views to remove")
    return existing_extra

def find_existing_dimension_tables():
    """Find existing dimension tables that should be used instead"""
    logger.info("\n🔍 FINDING EXISTING DIMENSION TABLES")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return {}
    
    cursor = conn.cursor()
    
    # Look for existing dimension tables with priority/status/resolution data
    cursor.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'load' 
        AND table_name LIKE 'dim_%'
        AND table_name NOT IN ('dim_priorities', 'dim_statuses', 'dim_resolutions', 'dim_issue_types')
        ORDER BY table_name
    """)
    
    existing_dims = [row[0] for row in cursor.fetchall()]
    
    # Check which ones might contain the data we need
    suitable_dims = {}
    
    for dim_table in existing_dims:
        cursor.execute(f"""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = 'load' AND table_name = '{dim_table}'
            AND (column_name LIKE '%priority%' OR column_name LIKE '%status%' 
                 OR column_name LIKE '%resolution%' OR column_name LIKE '%type%')
        """)
        
        relevant_columns = [row[0] for row in cursor.fetchall()]
        
        if relevant_columns:
            suitable_dims[dim_table] = relevant_columns
            logger.info(f"  ✅ {dim_table}: {', '.join(relevant_columns)}")
    
    cursor.close()
    conn.close()
    
    logger.info(f"📊 Found {len(suitable_dims)} existing dimension tables with relevant data")
    return suitable_dims

def drop_foreign_key_constraints():
    """Drop FK constraints that reference the extra tables"""
    logger.info("\n🔗 DROPPING FK CONSTRAINTS TO EXTRA TABLES")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Find FK constraints that reference the extra tables
    extra_tables = ['dim_priorities', 'dim_statuses', 'dim_resolutions', 'dim_issue_types']
    
    constraints_to_drop = []
    
    for table in extra_tables:
        cursor.execute("""
            SELECT 
                tc.constraint_name,
                tc.table_name
            FROM information_schema.table_constraints AS tc 
            JOIN information_schema.constraint_column_usage AS ccu
                ON ccu.constraint_name = tc.constraint_name
                AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY' 
            AND tc.table_schema = 'load'
            AND ccu.table_name = %s
        """, (table,))
        
        for constraint_name, fact_table in cursor.fetchall():
            constraints_to_drop.append((fact_table, constraint_name))
    
    # Drop the constraints
    success_count = 0
    for fact_table, constraint_name in constraints_to_drop:
        try:
            cursor.execute(f'ALTER TABLE load."{fact_table}" DROP CONSTRAINT "{constraint_name}"')
            logger.info(f"  ✅ Dropped constraint: {constraint_name}")
            success_count += 1
        except Exception as e:
            logger.error(f"  ❌ Error dropping {constraint_name}: {e}")
            conn.rollback()
    
    conn.commit()
    cursor.close()
    conn.close()
    
    logger.info(f"📊 Dropped {success_count} FK constraints")
    return success_count > 0

def drop_extra_tables():
    """Drop the extra tables and views I created"""
    logger.info("\n🗑️ DROPPING EXTRA TABLES AND VIEWS")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Tables to drop
    tables_to_drop = [
        'dim_priorities',
        'dim_statuses', 
        'dim_resolutions',
        'dim_issue_types'
    ]
    
    # Views to drop
    views_to_drop = [
        'dim_reporters',
        'dim_assignees', 
        'dim_creators'
    ]
    
    success_count = 0
    
    # Drop views first
    for view in views_to_drop:
        try:
            cursor.execute(f'DROP VIEW IF EXISTS load."{view}"')
            logger.info(f"  ✅ Dropped view: {view}")
            success_count += 1
        except Exception as e:
            logger.error(f"  ❌ Error dropping view {view}: {e}")
            conn.rollback()
    
    # Drop tables
    for table in tables_to_drop:
        try:
            cursor.execute(f'DROP TABLE IF EXISTS load."{table}" CASCADE')
            logger.info(f"  ✅ Dropped table: {table}")
            success_count += 1
        except Exception as e:
            logger.error(f"  ❌ Error dropping table {table}: {e}")
            conn.rollback()
    
    conn.commit()
    cursor.close()
    conn.close()
    
    logger.info(f"📊 Dropped {success_count} tables/views")
    return success_count > 0

def remove_extra_columns():
    """Remove the extra ID columns I added to fact_issues"""
    logger.info("\n🔧 REMOVING EXTRA COLUMNS")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Columns I added that should be removed
    columns_to_remove = [
        'priority_id',
        'status_id',
        'resolution_id', 
        'issue_type_id'
    ]
    
    success_count = 0
    
    for column in columns_to_remove:
        try:
            # Check if column exists
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.columns 
                WHERE table_schema = 'load' AND table_name = 'fact_issues' AND column_name = %s
            """, (column,))
            
            if cursor.fetchone()[0] > 0:
                cursor.execute(f'ALTER TABLE load.fact_issues DROP COLUMN "{column}"')
                logger.info(f"  ✅ Removed column: fact_issues.{column}")
                success_count += 1
            else:
                logger.info(f"  ⚠️ Column doesn't exist: fact_issues.{column}")
                
        except Exception as e:
            logger.error(f"  ❌ Error removing column {column}: {e}")
            conn.rollback()
    
    conn.commit()
    cursor.close()
    conn.close()
    
    logger.info(f"📊 Removed {success_count} extra columns")
    return success_count >= 0

def validate_cleanup():
    """Validate that we now have exactly 40 tables"""
    logger.info("\n✅ VALIDATING CLEANUP")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Count tables
    cursor.execute("""
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'load'
    """)
    table_count = cursor.fetchone()[0]
    
    # Count views
    cursor.execute("""
        SELECT COUNT(*) FROM information_schema.views 
        WHERE table_schema = 'load'
    """)
    view_count = cursor.fetchone()[0]
    
    # Count FK constraints
    cursor.execute("""
        SELECT COUNT(*) FROM information_schema.table_constraints 
        WHERE table_schema = 'load' AND constraint_type = 'FOREIGN KEY'
    """)
    fk_count = cursor.fetchone()[0]
    
    cursor.close()
    conn.close()
    
    logger.info(f"📊 Final counts:")
    logger.info(f"  🏗️ Tables: {table_count} (target: 40)")
    logger.info(f"  👁️ Views: {view_count}")
    logger.info(f"  🔗 Foreign keys: {fk_count}")
    
    is_valid = table_count == 40
    
    if is_valid:
        logger.info("✅ Cleanup successful - exactly 40 tables!")
    else:
        logger.warning(f"⚠️ Still have {table_count} tables instead of 40")
    
    return is_valid

def main():
    """Main cleanup function"""
    logger.info("🧹 CLEANUP EXTRA TABLES")
    logger.info("="*70)
    logger.info("Removing extra dimension tables to get back to 40 tables...")
    logger.info("="*70)
    
    start_time = datetime.now()
    
    # Step 1: Identify extra tables
    extra_tables = identify_extra_tables()
    
    # Step 2: Find existing dimension tables
    existing_dims = find_existing_dimension_tables()
    
    # Step 3: Drop FK constraints
    fk_success = drop_foreign_key_constraints()
    
    # Step 4: Drop extra tables
    drop_success = drop_extra_tables()
    
    # Step 5: Remove extra columns
    column_success = remove_extra_columns()
    
    # Step 6: Validate cleanup
    validation_success = validate_cleanup()
    
    # Summary
    duration = datetime.now() - start_time
    logger.info("\n" + "="*70)
    logger.info("📊 CLEANUP SUMMARY")
    logger.info("="*70)
    logger.info(f"⏱️ Duration: {duration}")
    logger.info(f"🔍 Extra tables identified: {len(extra_tables)}")
    logger.info(f"🔗 FK constraints dropped: {'✅' if fk_success else '❌'}")
    logger.info(f"🗑️ Tables dropped: {'✅' if drop_success else '❌'}")
    logger.info(f"🔧 Columns removed: {'✅' if column_success else '❌'}")
    logger.info(f"✅ Validation: {'✅' if validation_success else '❌'}")
    
    overall_success = drop_success and column_success and validation_success
    
    if overall_success:
        logger.info("\n🎉 CLEANUP SUCCESSFUL!")
        logger.info("✅ Back to exactly 40 tables in load schema")
        logger.info("🎯 Ready to use existing dimension tables for FK relationships")
    else:
        logger.error("\n❌ Cleanup incomplete")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
