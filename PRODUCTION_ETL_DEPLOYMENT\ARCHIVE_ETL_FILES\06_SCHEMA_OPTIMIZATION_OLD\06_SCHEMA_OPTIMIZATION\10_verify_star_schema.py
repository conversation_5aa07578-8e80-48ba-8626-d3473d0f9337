#!/usr/bin/env python3
"""
🎯 VERIFY PERFECT STAR SCHEMA
Verify that all 39 dimension tables are connected to fact_issues with FK relationships
"""

import psycopg2
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_dw():
    return psycopg2.connect(
        host='localhost',
        database='aya',
        user='jirauser',
        password='mypassword'
    )

def verify_perfect_star_schema():
    """Verify the perfect star schema with proper counting"""
    logger.info("🎯 VERIFY PERFECT STAR SCHEMA")
    logger.info("="*70)
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    try:
        # Get all FK relationships from fact_issues
        cursor.execute("""
            SELECT 
                tc.constraint_name,
                kcu.column_name as fact_column,
                ccu.table_name AS dim_table,
                ccu.column_name AS dim_column
            FROM information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
                ON ccu.constraint_name = tc.constraint_name
                AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = 'load'
            AND tc.table_name = 'fact_issues'
            ORDER BY ccu.table_name, kcu.column_name
        """)
        
        fk_relationships = cursor.fetchall()
        
        # Count unique dimension tables referenced
        unique_dim_tables = set()
        fk_details = []
        
        for fk in fk_relationships:
            constraint_name, fact_column, dim_table, dim_column = fk
            unique_dim_tables.add(dim_table)
            fk_details.append({
                'constraint': constraint_name,
                'fact_column': fact_column,
                'dim_table': dim_table,
                'dim_column': dim_column
            })
        
        # Get total dimension tables
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'load' 
            AND table_name LIKE 'dim_%'
        """)
        
        total_dim_tables = cursor.fetchone()[0]
        
        # Display results
        logger.info(f"📊 STAR SCHEMA ANALYSIS:")
        logger.info(f"   Total dimension tables: {total_dim_tables}")
        logger.info(f"   Total FK relationships: {len(fk_relationships)}")
        logger.info(f"   Unique dimensions referenced: {len(unique_dim_tables)}")
        
        logger.info(f"\n🔗 FK RELATIONSHIPS BY DIMENSION:")
        
        # Group by dimension table
        dim_fks = {}
        for fk in fk_details:
            dim_table = fk['dim_table']
            if dim_table not in dim_fks:
                dim_fks[dim_table] = []
            dim_fks[dim_table].append(fk)
        
        for dim_table in sorted(dim_fks.keys()):
            fks = dim_fks[dim_table]
            if len(fks) == 1:
                fk = fks[0]
                logger.info(f"   ✅ {dim_table} ← fact_issues.{fk['fact_column']}")
            else:
                logger.info(f"   ✅ {dim_table} ← fact_issues.[{', '.join([f['fact_column'] for f in fks])}]")
        
        # Check if we have perfect star schema
        if len(unique_dim_tables) == total_dim_tables:
            logger.info(f"\n🎯 PERFECT STAR SCHEMA ACHIEVED!")
            logger.info(f"   ✅ All {total_dim_tables} dimension tables connected to fact_issues")
            logger.info(f"   ✅ {len(fk_relationships)} total FK relationships established")
            logger.info(f"   ✅ Complete star schema ready for analytics")
            
            # Special note about dim_users
            dim_users_fks = [fk for fk in fk_details if fk['dim_table'] == 'dim_users']
            if len(dim_users_fks) > 1:
                logger.info(f"   🌟 DENORMALIZED DESIGN: dim_users has {len(dim_users_fks)} FK relationships")
                for fk in dim_users_fks:
                    logger.info(f"      → {fk['fact_column']} (for {fk['fact_column'].replace('_id', '')} role)")
            
            return True
        else:
            missing_dims = total_dim_tables - len(unique_dim_tables)
            logger.info(f"\n⚠️ INCOMPLETE STAR SCHEMA")
            logger.info(f"   Missing connections to {missing_dims} dimension tables")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error verifying schema: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

def main():
    """Main verification function"""
    success = verify_perfect_star_schema()
    
    if success:
        logger.info(f"\n🎉 VERIFICATION SUCCESS!")
        logger.info(f"   🌟 PERFECT DENORMALIZED STAR SCHEMA VERIFIED!")
        logger.info(f"   🔗 All 39 dimension tables connected to fact_issues")
        logger.info(f"   📈 Ready for enterprise-grade analytics")
        logger.info(f"   🚀 Deployment ready!")
        return 0
    else:
        logger.info(f"\n❌ Star schema verification failed")
        return 1

if __name__ == "__main__":
    exit(main())
