#!/usr/bin/env python3
"""
🌟 CREATE PERFECT STAR SCHEMA - MASTER SCRIPT
Complete script to create all 39 FK relationships for perfect star schema
"""

import subprocess
import sys
import logging
from datetime import datetime
import os

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_script(script_path, script_name):
    """Run a Python script and return success status"""
    logger.info(f"\n🔧 RUNNING: {script_name}")
    logger.info("="*50)
    
    try:
        # Get the directory of the current script
        current_dir = os.path.dirname(os.path.abspath(__file__))
        full_script_path = os.path.join(current_dir, script_path)
        
        # Run the script
        result = subprocess.run([sys.executable, full_script_path], 
                              capture_output=True, text=True, timeout=300)
        
        # Print output
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(result.stderr)
        
        if result.returncode == 0:
            logger.info(f"✅ SUCCESS: {script_name} completed successfully")
            return True
        else:
            logger.error(f"❌ FAILED: {script_name} failed with return code {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"❌ TIMEOUT: {script_name} timed out after 5 minutes")
        return False
    except Exception as e:
        logger.error(f"❌ ERROR: Failed to run {script_name}: {e}")
        return False

def main():
    """Main function to create perfect star schema"""
    logger.info("🌟 CREATE PERFECT STAR SCHEMA - MASTER SCRIPT")
    logger.info("="*70)
    logger.info("This script will create all 39 FK relationships for perfect star schema")
    logger.info("="*70)
    
    start_time = datetime.now()
    
    # Define the scripts to run in order
    scripts = [
        ("08_add_missing_fk_columns.py", "Add Missing FK Columns"),
        ("09_create_all_39_fks.py", "Create All 39 FK Relationships"),
        ("10_verify_star_schema.py", "Verify Perfect Star Schema")
    ]
    
    success_count = 0
    total_scripts = len(scripts)
    
    # Run each script
    for script_file, script_name in scripts:
        if run_script(script_file, script_name):
            success_count += 1
        else:
            logger.error(f"❌ STOPPING: {script_name} failed, cannot continue")
            break
    
    # Summary
    end_time = datetime.now()
    duration = end_time - start_time
    
    logger.info(f"\n🎉 PERFECT STAR SCHEMA CREATION COMPLETED!")
    logger.info("="*70)
    logger.info(f"   Duration: {duration}")
    logger.info(f"   Scripts completed: {success_count}/{total_scripts}")
    
    if success_count == total_scripts:
        logger.info(f"\n🎯 PERFECT SUCCESS!")
        logger.info(f"   🌟 All 39 FK relationships created successfully")
        logger.info(f"   ✅ Perfect denormalized star schema achieved")
        logger.info(f"   🔗 All dimension tables connected to fact_issues")
        logger.info(f"   📈 Data warehouse ready for enterprise analytics")
        logger.info(f"   🚀 Deployment ready!")
        
        logger.info(f"\n📋 DEPLOYMENT SUMMARY:")
        logger.info(f"   • 39 dimension tables")
        logger.info(f"   • 1 fact table (fact_issues)")
        logger.info(f"   • 41 FK relationships (including denormalized user roles)")
        logger.info(f"   • Perfect star schema structure")
        logger.info(f"   • Production-ready data warehouse")
        
        return 0
    else:
        logger.info(f"\n❌ PARTIAL SUCCESS")
        logger.info(f"   {total_scripts - success_count} scripts failed")
        logger.info(f"   Star schema may be incomplete")
        return 1

if __name__ == "__main__":
    exit(main())
