import psycopg2

def connect_all_dimensions():
    print("CONNECTING ALL DIMENSIONS TO FACT TABLE")
    
    conn = psycopg2.connect(host='localhost', database='aya', user='jirauser', password='mypassword')
    cursor = conn.cursor()
    
    # Get all dimension tables
    cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'load' AND table_name LIKE 'dim_%' ORDER BY table_name")
    dim_tables = [row[0] for row in cursor.fetchall()]
    
    print(f"Found {len(dim_tables)} dimension tables")
    
    success_count = 0
    
    for dim_table in dim_tables:
        if dim_table in ['dim_users', 'dim_instances']:
            continue  # Skip already connected tables
            
        try:
            # Add FK column to fact_issues
            fk_column = f"{dim_table}_id"
            
            # Check if column exists
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.columns 
                WHERE table_schema = 'load' AND table_name = 'fact_issues' AND column_name = %s
            """, (fk_column,))
            
            if cursor.fetchone()[0] == 0:
                cursor.execute(f'ALTER TABLE load.fact_issues ADD COLUMN "{fk_column}" INTEGER')
                print(f"  Added column: {fk_column}")
            
            # Create FK constraint
            constraint_name = f"fk_fact_issues_{dim_table}"
            
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.table_constraints 
                WHERE table_schema = 'load' AND constraint_name = %s
            """, (constraint_name,))
            
            if cursor.fetchone()[0] == 0:
                cursor.execute(f'''
                    ALTER TABLE load.fact_issues 
                    ADD CONSTRAINT "{constraint_name}" 
                    FOREIGN KEY ("{fk_column}") 
                    REFERENCES load."{dim_table}" ("id")
                    ON DELETE SET NULL
                ''')
                print(f"  Created FK: fact_issues.{fk_column} -> {dim_table}.id")
                success_count += 1
            else:
                print(f"  FK already exists: {constraint_name}")
                success_count += 1
                
        except Exception as e:
            print(f"  Error with {dim_table}: {e}")
            conn.rollback()
    
    conn.commit()
    
    # Final count
    cursor.execute("SELECT COUNT(*) FROM information_schema.table_constraints WHERE table_schema = 'load' AND constraint_type = 'FOREIGN KEY'")
    total_fks = cursor.fetchone()[0]
    
    print(f"\nCOMPLETE! Total FK relationships: {total_fks}")
    print(f"Successfully connected {success_count} dimension tables")
    
    cursor.close()
    conn.close()
    
    return success_count

if __name__ == "__main__":
    connect_all_dimensions()
