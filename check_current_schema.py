#!/usr/bin/env python3
"""
🔍 CHECK CURRENT SCHEMA STRUCTURE
Analyze the current fact_issues table and all dimension tables to understand FK issues
"""

import psycopg2
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_dw():
    return psycopg2.connect(
        host='localhost',
        database='aya',
        user='jirauser',
        password='mypassword'
    )

def check_fact_issues_structure():
    """Check the current structure of fact_issues table"""
    logger.info("🔍 CHECKING FACT_ISSUES TABLE STRUCTURE")
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    try:
        # Get table structure
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_schema = 'load' 
            AND table_name = 'fact_issues'
            ORDER BY ordinal_position
        """)
        
        columns = cursor.fetchall()
        
        logger.info(f"📋 FACT_ISSUES has {len(columns)} columns:")
        for col in columns:
            logger.info(f"   {col[0]} | {col[1]} | {'NULL' if col[2] == 'YES' else 'NOT NULL'}")
            
        return columns
        
    except Exception as e:
        logger.error(f"❌ Error checking fact_issues: {e}")
        return []
    finally:
        cursor.close()
        conn.close()

def check_dimension_tables():
    """Check all dimension tables in load schema"""
    logger.info("\n🔍 CHECKING ALL DIMENSION TABLES")
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    try:
        # Get all dimension tables
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'load' 
            AND table_name LIKE 'dim_%'
            ORDER BY table_name
        """)
        
        dim_tables = [row[0] for row in cursor.fetchall()]
        
        logger.info(f"📋 Found {len(dim_tables)} dimension tables:")
        for table in dim_tables:
            logger.info(f"   ✅ {table}")
            
        return dim_tables
        
    except Exception as e:
        logger.error(f"❌ Error checking dimension tables: {e}")
        return []
    finally:
        cursor.close()
        conn.close()

def check_existing_foreign_keys():
    """Check existing foreign key constraints"""
    logger.info("\n🔍 CHECKING EXISTING FOREIGN KEYS")
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    try:
        cursor.execute("""
            SELECT 
                tc.constraint_name,
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name
            FROM information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
                ON ccu.constraint_name = tc.constraint_name
                AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = 'load'
            AND tc.table_name = 'fact_issues'
            ORDER BY tc.constraint_name
        """)
        
        fks = cursor.fetchall()
        
        logger.info(f"📋 Found {len(fks)} existing foreign keys:")
        for fk in fks:
            logger.info(f"   ✅ {fk[1]}.{fk[2]} → {fk[3]}.{fk[4]} ({fk[0]})")
            
        return fks
        
    except Exception as e:
        logger.error(f"❌ Error checking foreign keys: {e}")
        return []
    finally:
        cursor.close()
        conn.close()

def analyze_potential_fk_columns():
    """Analyze which columns in fact_issues could be foreign keys"""
    logger.info("\n🔍 ANALYZING POTENTIAL FK COLUMNS")
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    try:
        # Get fact_issues columns that end with _id
        cursor.execute("""
            SELECT column_name, data_type
            FROM information_schema.columns 
            WHERE table_schema = 'load' 
            AND table_name = 'fact_issues'
            AND (column_name LIKE '%_id' OR column_name LIKE '%id')
            ORDER BY column_name
        """)
        
        potential_fk_columns = cursor.fetchall()
        
        logger.info(f"📋 Found {len(potential_fk_columns)} potential FK columns:")
        for col in potential_fk_columns:
            logger.info(f"   🔗 {col[0]} ({col[1]})")
            
        return potential_fk_columns
        
    except Exception as e:
        logger.error(f"❌ Error analyzing FK columns: {e}")
        return []
    finally:
        cursor.close()
        conn.close()

def check_dimension_primary_keys():
    """Check primary key columns in dimension tables"""
    logger.info("\n🔍 CHECKING DIMENSION TABLE PRIMARY KEYS")
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    try:
        cursor.execute("""
            SELECT 
                t.table_name,
                c.column_name,
                c.data_type
            FROM information_schema.tables t
            JOIN information_schema.table_constraints tc 
                ON t.table_name = tc.table_name 
                AND t.table_schema = tc.table_schema
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
            JOIN information_schema.columns c
                ON t.table_name = c.table_name
                AND t.table_schema = c.table_schema
                AND kcu.column_name = c.column_name
            WHERE t.table_schema = 'load'
            AND t.table_name LIKE 'dim_%'
            AND tc.constraint_type = 'PRIMARY KEY'
            ORDER BY t.table_name
        """)
        
        pk_info = cursor.fetchall()
        
        logger.info(f"📋 Primary keys in dimension tables:")
        for pk in pk_info:
            logger.info(f"   🔑 {pk[0]}.{pk[1]} ({pk[2]})")
            
        return pk_info
        
    except Exception as e:
        logger.error(f"❌ Error checking primary keys: {e}")
        return []
    finally:
        cursor.close()
        conn.close()

def main():
    """Main analysis function"""
    logger.info("🔍 CURRENT SCHEMA ANALYSIS")
    logger.info("="*70)
    
    # Check fact_issues structure
    fact_columns = check_fact_issues_structure()
    
    # Check dimension tables
    dim_tables = check_dimension_tables()
    
    # Check existing foreign keys
    existing_fks = check_existing_foreign_keys()
    
    # Analyze potential FK columns
    potential_fks = analyze_potential_fk_columns()
    
    # Check dimension primary keys
    dim_pks = check_dimension_primary_keys()
    
    # Summary
    logger.info("\n📊 ANALYSIS SUMMARY")
    logger.info("="*50)
    logger.info(f"   Fact table columns: {len(fact_columns)}")
    logger.info(f"   Dimension tables: {len(dim_tables)}")
    logger.info(f"   Existing FKs: {len(existing_fks)}")
    logger.info(f"   Potential FK columns: {len(potential_fks)}")
    logger.info(f"   Dimension PKs: {len(dim_pks)}")
    
    if len(existing_fks) < len(dim_tables):
        missing_fks = len(dim_tables) - len(existing_fks)
        logger.info(f"   ⚠️ Missing FKs: {missing_fks}")
        logger.info(f"   🎯 Target: {len(dim_tables)} FK relationships")

if __name__ == "__main__":
    main()
