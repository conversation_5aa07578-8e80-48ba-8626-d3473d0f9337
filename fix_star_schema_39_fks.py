#!/usr/bin/env python3
"""
🌟 FIX STAR SCHEMA - CREATE ALL 39 FOREIGN KEYS
Add missing FK columns to fact_issues and create all 39 FK relationships for perfect star schema
"""

import psycopg2
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_dw():
    return psycopg2.connect(
        host='localhost',
        database='aya',
        user='jirauser',
        password='mypassword'
    )

def add_missing_fk_columns():
    """Add missing FK columns to fact_issues table"""
    logger.info("🔧 ADDING MISSING FK COLUMNS TO FACT_ISSUES")
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    # Define all FK columns needed (based on dimension tables)
    missing_fk_columns = [
        # Existing columns (already present): assignee_id, creator_id, reporter_id, instance_id, project_id, component_id, workflow_id
        
        # Missing FK columns to add:
        "dim_agile_id INTEGER",
        "dim_ao_4b00e6_sr_user_prop_id INTEGER", 
        "dim_ao_4b00e6_stash_settings_id INTEGER",
        "dim_app_user_id INTEGER",
        "attachment_id BIGINT",  # for dim_attachments
        "changegroup_id BIGINT",  # for dim_changegroups
        "dim_changeitem_id INTEGER",
        "customfield_id BIGINT",  # for dim_customfields
        "dim_customfieldvalue_id INTEGER",
        "dim_cwd_directory_id INTEGER",
        "dim_cwd_group_id INTEGER",
        "dim_cwd_membership_id INTEGER",
        "dim_cwd_user_attributes_id INTEGER",
        "dim_data_quality_id INTEGER",
        "date_id INTEGER",  # for dim_dates
        "dim_field_configs_id INTEGER",
        "dim_issuelink_id INTEGER",
        "dim_issuelinktype_id INTEGER",
        "dim_issues_metadata_id INTEGER",
        "dim_jiraaction_id INTEGER",
        "dim_label_id INTEGER",
        "dim_managedconfigurationitem_id INTEGER",
        "dim_nodeassociation_id INTEGER",
        "dim_os_currentstep_id INTEGER",
        "dim_os_wfentry_id INTEGER",
        "dim_permissions_id INTEGER",
        "dim_pluginversion_id INTEGER",
        "dim_projectcategory_id INTEGER",
        "dim_projectroleactor_id INTEGER",
        "dim_projectversion_id INTEGER",
        "dim_screens_id INTEGER",
        "dim_userassociation_id INTEGER",
        "dim_workflow_schemes_id INTEGER",
        "worklog_id BIGINT"  # for dim_worklogs
    ]
    
    success_count = 0
    
    for column_def in missing_fk_columns:
        try:
            # Check if column already exists
            column_name = column_def.split()[0]
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_schema = 'load' 
                AND table_name = 'fact_issues' 
                AND column_name = %s
            """, (column_name,))
            
            if cursor.fetchone():
                logger.info(f"   ✅ Column {column_name} already exists")
                continue
            
            # Add the column
            alter_sql = f"ALTER TABLE load.fact_issues ADD COLUMN {column_def}"
            cursor.execute(alter_sql)
            conn.commit()
            
            logger.info(f"   ✅ Added column: {column_name}")
            success_count += 1
            
        except Exception as e:
            logger.error(f"   ❌ Error adding {column_name}: {e}")
            conn.rollback()
    
    cursor.close()
    conn.close()
    
    logger.info(f"📊 ADDED {success_count} FK COLUMNS")
    return success_count > 0

def create_all_foreign_keys():
    """Create all 39 foreign key relationships"""
    logger.info("\n🔗 CREATING ALL 39 FOREIGN KEY RELATIONSHIPS")
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    # Define all FK relationships
    foreign_keys = [
        # Existing FKs (already created)
        # ("fact_issues", "assignee_id", "dim_users", "user_id", "fk_fact_issues_assignee_denorm"),
        # ("fact_issues", "creator_id", "dim_users", "user_id", "fk_fact_issues_creator_denorm"),
        # ("fact_issues", "reporter_id", "dim_users", "user_id", "fk_fact_issues_reporter_denorm"),
        # ("fact_issues", "instance_id", "dim_instances", "instance_id", "fk_fact_issues_instance"),
        
        # New FKs to create
        ("fact_issues", "dim_agile_id", "dim_agile", "dim_agile_id", "fk_fact_issues_agile"),
        ("fact_issues", "dim_ao_4b00e6_sr_user_prop_id", "dim_ao_4b00e6_sr_user_prop", "dim_ao_4b00e6_sr_user_prop_id", "fk_fact_issues_sr_user_prop"),
        ("fact_issues", "dim_ao_4b00e6_stash_settings_id", "dim_ao_4b00e6_stash_settings", "dim_ao_4b00e6_stash_settings_id", "fk_fact_issues_stash_settings"),
        ("fact_issues", "dim_app_user_id", "dim_app_user", "dim_app_user_id", "fk_fact_issues_app_user"),
        ("fact_issues", "attachment_id", "dim_attachments", "attachment_id", "fk_fact_issues_attachments"),
        ("fact_issues", "changegroup_id", "dim_changegroups", "changegroup_id", "fk_fact_issues_changegroups"),
        ("fact_issues", "dim_changeitem_id", "dim_changeitem", "dim_changeitem_id", "fk_fact_issues_changeitem"),
        ("fact_issues", "component_id", "dim_components", "component_id", "fk_fact_issues_components"),
        ("fact_issues", "customfield_id", "dim_customfields", "customfield_id", "fk_fact_issues_customfields"),
        ("fact_issues", "dim_customfieldvalue_id", "dim_customfieldvalue", "dim_customfieldvalue_id", "fk_fact_issues_customfieldvalue"),
        ("fact_issues", "dim_cwd_directory_id", "dim_cwd_directory", "dim_cwd_directory_id", "fk_fact_issues_cwd_directory"),
        ("fact_issues", "dim_cwd_group_id", "dim_cwd_group", "dim_cwd_group_id", "fk_fact_issues_cwd_group"),
        ("fact_issues", "dim_cwd_membership_id", "dim_cwd_membership", "dim_cwd_membership_id", "fk_fact_issues_cwd_membership"),
        ("fact_issues", "dim_cwd_user_attributes_id", "dim_cwd_user_attributes", "dim_cwd_user_attributes_id", "fk_fact_issues_cwd_user_attributes"),
        ("fact_issues", "dim_data_quality_id", "dim_data_quality", "dim_data_quality_id", "fk_fact_issues_data_quality"),
        ("fact_issues", "date_id", "dim_dates", "date_id", "fk_fact_issues_dates"),
        ("fact_issues", "dim_field_configs_id", "dim_field_configs", "dim_field_configs_id", "fk_fact_issues_field_configs"),
        ("fact_issues", "dim_issuelink_id", "dim_issuelink", "dim_issuelink_id", "fk_fact_issues_issuelink"),
        ("fact_issues", "dim_issuelinktype_id", "dim_issuelinktype", "dim_issuelinktype_id", "fk_fact_issues_issuelinktype"),
        ("fact_issues", "dim_issues_metadata_id", "dim_issues_metadata", "dim_issues_metadata_id", "fk_fact_issues_issues_metadata"),
        ("fact_issues", "dim_jiraaction_id", "dim_jiraaction", "dim_jiraaction_id", "fk_fact_issues_jiraaction"),
        ("fact_issues", "dim_label_id", "dim_label", "dim_label_id", "fk_fact_issues_label"),
        ("fact_issues", "dim_managedconfigurationitem_id", "dim_managedconfigurationitem", "dim_managedconfigurationitem_id", "fk_fact_issues_managedconfigurationitem"),
        ("fact_issues", "dim_nodeassociation_id", "dim_nodeassociation", "dim_nodeassociation_id", "fk_fact_issues_nodeassociation"),
        ("fact_issues", "dim_os_currentstep_id", "dim_os_currentstep", "dim_os_currentstep_id", "fk_fact_issues_os_currentstep"),
        ("fact_issues", "dim_os_wfentry_id", "dim_os_wfentry", "dim_os_wfentry_id", "fk_fact_issues_os_wfentry"),
        ("fact_issues", "dim_permissions_id", "dim_permissions", "dim_permissions_id", "fk_fact_issues_permissions"),
        ("fact_issues", "dim_pluginversion_id", "dim_pluginversion", "dim_pluginversion_id", "fk_fact_issues_pluginversion"),
        ("fact_issues", "dim_projectcategory_id", "dim_projectcategory", "dim_projectcategory_id", "fk_fact_issues_projectcategory"),
        ("fact_issues", "project_id", "dim_projects", "project_id", "fk_fact_issues_projects"),
        ("fact_issues", "dim_projectroleactor_id", "dim_projectroleactor", "dim_projectroleactor_id", "fk_fact_issues_projectroleactor"),
        ("fact_issues", "dim_projectversion_id", "dim_projectversion", "dim_projectversion_id", "fk_fact_issues_projectversion"),
        ("fact_issues", "dim_screens_id", "dim_screens", "dim_screens_id", "fk_fact_issues_screens"),
        ("fact_issues", "dim_userassociation_id", "dim_userassociation", "dim_userassociation_id", "fk_fact_issues_userassociation"),
        ("fact_issues", "dim_workflow_schemes_id", "dim_workflow_schemes", "dim_workflow_schemes_id", "fk_fact_issues_workflow_schemes"),
        ("fact_issues", "workflow_id", "dim_workflows", "workflow_id", "fk_fact_issues_workflows"),
        ("fact_issues", "worklog_id", "dim_worklogs", "worklog_id", "fk_fact_issues_worklogs")
    ]
    
    success_count = 0
    
    for fact_table, fact_column, dim_table, dim_column, constraint_name in foreign_keys:
        try:
            # Check if FK already exists
            cursor.execute("""
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_schema = 'load' 
                AND table_name = %s 
                AND constraint_name = %s
                AND constraint_type = 'FOREIGN KEY'
            """, (fact_table, constraint_name))
            
            if cursor.fetchone():
                logger.info(f"   ✅ FK {constraint_name} already exists")
                continue
            
            # Create foreign key constraint
            cursor.execute(f'''
                ALTER TABLE load."{fact_table}" 
                ADD CONSTRAINT "{constraint_name}" 
                FOREIGN KEY ("{fact_column}") 
                REFERENCES load."{dim_table}" ("{dim_column}")
                ON DELETE SET NULL
                ON UPDATE CASCADE
            ''')
            
            conn.commit()
            logger.info(f"   ✅ Created FK: {fact_table}.{fact_column} → {dim_table}.{dim_column}")
            success_count += 1
            
        except Exception as e:
            logger.error(f"   ❌ Error creating {constraint_name}: {e}")
            conn.rollback()
    
    cursor.close()
    conn.close()
    
    logger.info(f"📊 CREATED {success_count} NEW FOREIGN KEYS")
    return success_count

def verify_star_schema():
    """Verify the complete star schema with all 39 FK relationships"""
    logger.info("\n🔍 VERIFYING COMPLETE STAR SCHEMA")

    conn = connect_dw()
    cursor = conn.cursor()

    try:
        # Count total FK relationships
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.table_constraints
            WHERE table_schema = 'load'
            AND table_name = 'fact_issues'
            AND constraint_type = 'FOREIGN KEY'
        """)

        total_fks = cursor.fetchone()[0]

        # Count dimension tables
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'load'
            AND table_name LIKE 'dim_%'
        """)

        total_dims = cursor.fetchone()[0]

        logger.info(f"📊 STAR SCHEMA VERIFICATION:")
        logger.info(f"   Dimension tables: {total_dims}")
        logger.info(f"   Foreign keys: {total_fks}")

        if total_fks == total_dims:
            logger.info(f"   🎯 PERFECT! All {total_dims} dimensions connected to fact table")
            return True
        else:
            missing = total_dims - total_fks
            logger.info(f"   ⚠️ Missing {missing} FK relationships")
            return False

    except Exception as e:
        logger.error(f"❌ Error verifying schema: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

def main():
    """Main function to fix star schema"""
    logger.info("🌟 FIX STAR SCHEMA - CREATE ALL 39 FOREIGN KEYS")
    logger.info("="*70)

    start_time = datetime.now()

    # Step 1: Add missing FK columns
    logger.info("STEP 1: Adding missing FK columns...")
    if not add_missing_fk_columns():
        logger.error("❌ Failed to add FK columns")
        return 1

    # Step 2: Create all foreign key relationships
    logger.info("\nSTEP 2: Creating all foreign key relationships...")
    created_fks = create_all_foreign_keys()

    # Step 3: Verify complete star schema
    logger.info("\nSTEP 3: Verifying complete star schema...")
    is_perfect = verify_star_schema()

    # Summary
    end_time = datetime.now()
    duration = end_time - start_time

    logger.info(f"\n🎉 STAR SCHEMA FIX COMPLETED!")
    logger.info(f"   Duration: {duration}")
    logger.info(f"   New FKs created: {created_fks}")

    if is_perfect:
        logger.info(f"   🎯 SUCCESS: PERFECT STAR SCHEMA WITH 39 FK RELATIONSHIPS!")
        logger.info(f"   ✅ All dimension tables connected to fact_issues")
        logger.info(f"   ✅ Complete star schema ready for analytics")
        return 0
    else:
        logger.info(f"   ⚠️ PARTIAL SUCCESS: Some FK relationships may be missing")
        return 1

if __name__ == "__main__":
    exit(main())
